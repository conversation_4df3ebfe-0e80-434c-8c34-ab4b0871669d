/**
 * 聊天室页面 - 重写版
 */
import API from '../../../utils/api.js';
import imageOptimizer from '../../../utils/imageOptimizer.js';

const app = getApp();

Page({
  data: {
    roomId: '',
    orderNo: '',
    messageList: [],
    inputText: '',
    canSend: false, // 添加一个明确的发送状态标志
    loading: false,
    page: 1,
    pageSize: 20,
    hasMore: true,
    userInfo: null,
    chatRoomInfo: null,
    scrollToView: '',
    scrollTop: 0, // 滚动位置
    themeLoaded: true, // 立即设置主题加载状态，避免白色背景闪烁
    messageWatcher: null, // 消息监听器
    isWatcherActive: false, // 监听器状态
    // 新增的UI状态
    showAttachmentPanel: false, // 附件面板显示状态
    showEmojiPanel: false, // 表情包面板显示状态
    // 语音录制相关
    isVoiceMode: false,        // 是否为语音模式
    isRecording: false,        // 是否正在录音
    hasRecording: false,       // 是否有录音
    recordTime: 0,             // 录音时长（秒）
    recordTimeDisplay: '0"',   // 录音时长显示
    currentVoicePath: '',      // 当前录音文件路径
    isCancelMode: false,       // 是否为取消模式（上滑取消）
    touchStartY: 0,            // 触摸开始Y坐标
    recordingTip: '松开发送', // 录音提示文字
    // 表情包数据
    emojiList: [
      // 常用表情
      { category: '常用', emojis: ['😊', '😂', '🥰', '😍', '🤔', '😅', '😭', '😘', '🙄', '😴', '🤗', '🥺'] },
      // 人物表情
      { category: '人物', emojis: ['😀', '😃', '😄', '😁', '😆', '😅', '🤣', '😂', '🙂', '🙃', '😉', '😊', '😇', '🥰', '😍', '🤩', '😘', '😗', '😚', '😙', '🥲', '😋', '😛', '😜', '🤪', '😝', '🤑', '🤗', '🤭', '🤫', '🤔', '🤐', '🤨', '😐', '😑', '😶', '😏', '😒', '🙄', '😬', '🤥', '😔', '😪', '🤤', '😴', '😷', '🤒', '🤕', '🤢', '🤮', '🤧', '🥵', '🥶', '🥴', '😵', '🤯', '🤠', '🥳', '🥸', '😎', '🤓', '🧐'] },
      // 手势
      { category: '手势', emojis: ['👍', '👎', '👌', '✌️', '🤞', '🤟', '🤘', '🤙', '👈', '👉', '👆', '🖕', '👇', '☝️', '👋', '🤚', '🖐️', '✋', '🖖', '👏', '🙌', '🤲', '🤝', '🙏'] },
      // 物品
      { category: '物品', emojis: ['❤️', '🧡', '💛', '💚', '💙', '💜', '🖤', '🤍', '🤎', '💔', '❣️', '💕', '💞', '💓', '💗', '💖', '💘', '💝', '💟', '☮️', '✝️', '☪️', '🕉️', '☸️', '✡️', '🔯', '🕎', '☯️', '☦️', '🛐', '⛎', '♈', '♉', '♊', '♋', '♌', '♍', '♎', '♏', '♐', '♑', '♒', '♓'] }
    ]
  },

  // 语音录制相关
  recorderManager: null,
  recordTimer: null,
  audioContext: null,
  currentPlayingAudio: null, // 当前播放的音频实例
  voicePermissionGranted: false, // 语音权限状态

  // 格式化聊天消息时间
  formatMessageTime(dateTime) {
    if (!dateTime) return '';

    try {
      const time = new Date(dateTime);

      // 检查日期是否有效
      if (isNaN(time.getTime())) {
        return '';
      }

      // 格式化为 YYYY-M-D-HH:mm 格式
      const year = time.getFullYear();
      const month = time.getMonth() + 1; // 不补零
      const day = time.getDate(); // 不补零
      const hour = String(time.getHours()).padStart(2, '0'); // 补零
      const minute = String(time.getMinutes()).padStart(2, '0'); // 补零

      return `${year}-${month}-${day}-${hour}:${minute}`;
    } catch (error) {
      console.error('时间格式化失败:', error);
      return '';
    }
  },

  onLoad(options) {
    // 立即隐藏任何可能的系统加载提示
    wx.hideLoading();

    // 立即设置主题加载状态，避免白色背景闪烁
    this.setData({ themeLoaded: true });

    const { roomId, orderNo, orderId } = options;

    // 如果有roomId，直接使用
    if (roomId) {
      this.setData({
        roomId: roomId,
        orderNo: orderNo || ''
      });

      // 设置当前活跃的聊天室（这会自动清除该聊天室的未读数）
      app.globalData.setActiveChatRoom(roomId);

      this.initUserInfo();
      this.initImageOptimizer();
      this.initVoiceRecorder();
      this.loadMessages(true).then(() => {
        // 初始消息加载完成后启动监听器
        this.startMessageWatcher();
      });
    }
    // 如果只有orderId，尝试查找或创建聊天室
    else if (orderId) {
      this.findOrCreateChatRoom(orderId);
    }
    // 参数不完整
    else {
      wx.showToast({
        title: '聊天室参数无效',
        icon: 'none'
      });
      setTimeout(() => {
        wx.navigateBack();
      }, 1500);
      return;
    }
  },

  onReady() {
    console.log('=== 聊天室页面就绪 ===');

    // 页面就绪后延迟滚动到底部
    setTimeout(() => {
      this.scrollToBottom();
    }, 500);
  },

  onShow() {
    // 确保隐藏任何可能的系统加载提示
    wx.hideLoading();

    // 页面显示时滚动到底部（延迟执行确保页面完全加载）
    setTimeout(() => {
      this.scrollToBottom();
    }, 200);

    // 如果监听器未启动，则启动监听器
    if (!this.data.isWatcherActive) {
      this.startMessageWatcher();
    }
  },

  onHide() {
    // 页面隐藏时停止监听器（节省资源）
    console.log('📱 [页面状态] 页面隐藏，停止监听器');
    this.stopMessageWatcher();
  },

  // 通过订单ID查找或创建聊天室
  async findOrCreateChatRoom(orderId) {
    try {
      wx.showLoading({
        title: '查找聊天室...',
        mask: true
      });

      // 调用API查找聊天室
      const result = await API.createChatRoom(orderId);

      if (result.success && result.data.chatRoomId) {
        // 设置聊天室ID并初始化
        this.setData({
          roomId: result.data.chatRoomId,
          orderNo: orderId
        });

        this.initUserInfo();
        this.loadMessages(true).then(() => {
          // 初始消息加载完成后启动监听器
          this.startMessageWatcher();
        });
      } else {
        wx.showToast({
          title: result.error || '聊天室不存在',
          icon: 'none'
        });
        setTimeout(() => {
          wx.navigateBack();
        }, 1500);
      }
    } catch (error) {
      console.error('❌ [聊天室] 查找异常:', error);
      wx.showToast({
        title: '网络错误',
        icon: 'none'
      });
      setTimeout(() => {
        wx.navigateBack();
      }, 1500);
    } finally {
      wx.hideLoading();
    }
  },

  // 初始化用户信息
  initUserInfo() {
    const userInfo = app.globalData.userInfo || wx.getStorageSync('userInfo');
    if (userInfo) {
      this.setData({ userInfo });
      console.log('✅ [用户信息] 用户:', userInfo._id, userInfo.nickName);
    } else {
      console.log('❌ [用户信息] 用户信息不存在');
      wx.showToast({
        title: '请先登录',
        icon: 'none'
      });
      setTimeout(() => {
        wx.navigateBack();
      }, 1500);
    }
  },

  // 加载消息列表（优化版）
  async loadMessages(refresh = false) {
    if (this.data.loading) {
      console.log('⚠️ [消息加载] 正在加载中，跳过重复请求');
      return Promise.resolve();
    }

    try {
      // 确保隐藏任何可能的系统加载提示
      wx.hideLoading();
      this.setData({ loading: true });

      const page = refresh ? 1 : this.data.page;
      const cacheKey = `${this.data.roomId}_page_${page}`;

      console.log(`🔄 [消息加载] 加载消息 - 页码: ${page}, 刷新: ${refresh}`);

      // 检查缓存（仅对历史消息使用缓存）
      if (!refresh && page > 1) {
        const cachedMessages = this.getMessageCache(cacheKey);
        if (cachedMessages) {
          console.log('📦 [消息缓存] 使用缓存数据');
          this.processCachedMessages(cachedMessages, page);
          return Promise.resolve();
        }
      }

      // 禁用系统加载提示，使用页面内的加载状态
      const result = await API.callFunction('chatMessage', {
        action: 'get',
        chatRoomId: this.data.roomId,
        page,
        pageSize: this.data.pageSize
      }, { showLoading: false });

      console.log('📋 [消息加载] 查询结果:', result);

      if (result.success) {
        const newMessages = result.data.list || [];

        // 处理消息数据，格式化时间和用户信息
        const processedMessages = newMessages.map(msg => {
          const processedMsg = {
            ...msg,
            formattedTime: this.formatMessageTime(msg.createTime),
            isSelf: msg.senderId === this.data.userInfo._id,
            isRecalled: msg.isRecalled || false,
            canRecall: this.canRecallMessage(msg)
          };
          
          // 特别处理语音消息的时长显示
          if (msg.type === 'voice') {
            console.log('🎵 [语音消息] 处理语音消息时长:', {
              messageId: msg._id,
              originalDuration: msg.duration,
              durationType: typeof msg.duration
            });
            
            // 确保时长是数字类型
            if (msg.duration && typeof msg.duration === 'number') {
              processedMsg.duration = msg.duration;
            } else if (msg.duration && typeof msg.duration === 'string') {
              processedMsg.duration = parseInt(msg.duration) || 0;
            } else {
              console.log('⚠️ [语音消息] 语音消息时长数据异常:', msg.duration);
              // 尝试从文件名中提取时长信息
              if (msg.content && typeof msg.content === 'string') {
                console.log('🔍 [语音消息] 尝试从URL提取时长:', msg.content);
                
                // 尝试匹配新格式：duration数字.mp3
                let durationMatch = msg.content.match(/duration(\d+)\.mp3/);
                if (durationMatch) {
                  const extractedDuration = Math.ceil(parseInt(durationMatch[1]) / 1000);
                  console.log('🎵 [语音消息] 从新格式文件名提取时长:', extractedDuration, '秒');
                  processedMsg.duration = extractedDuration;
                } else {
                  // 尝试匹配旧格式：durationTime=数字
                  durationMatch = msg.content.match(/durationTime=(\d+)/);
                  if (durationMatch) {
                    const extractedDuration = Math.ceil(parseInt(durationMatch[1]) / 1000);
                    console.log('🎵 [语音消息] 从旧格式文件名提取时长:', extractedDuration, '秒');
                    processedMsg.duration = extractedDuration;
                  } else {
                    console.log('🎵 [语音消息] 无法从文件名提取时长，设置为0');
                    processedMsg.duration = 0;
                  }
                }
              } else {
                console.log('🎵 [语音消息] 内容为空，设置时长为0');
                processedMsg.duration = 0;
              }
            }
            
            console.log('🎵 [语音消息] 处理后时长:', processedMsg.duration);
          }
          
          return processedMsg;
        });

        // 缓存历史消息（不缓存第一页）
        if (!refresh && page > 1 && processedMessages.length > 0) {
          this.setMessageCache(cacheKey, processedMessages);
        }

        // 处理数据
        let messageList;
        if (refresh || page === 1) {
          messageList = processedMessages;
          // 清理旧缓存
          this.clearOldMessageCache();
        } else {
          // 新消息添加到前面（历史消息）
          messageList = [...processedMessages, ...this.data.messageList];
        }

        // 性能优化：限制内存中的消息数量
        const maxMessages = 500; // 最多保留500条消息
        if (messageList.length > maxMessages) {
          messageList = messageList.slice(0, maxMessages);
          console.log(`📦 [性能优化] 限制消息数量为 ${maxMessages} 条`);
        }

        this.setData({
          messageList: messageList,
          page: page,
          hasMore: result.data.hasMore || false
        });

        console.log(`✅ [消息加载] 加载成功: ${messageList.length} 条消息`);

        // 首次加载或刷新时滚动到底部
        if (refresh || page === 1) {
          // 延迟滚动，确保DOM更新完成
          setTimeout(() => {
            this.scrollToBottom();
          }, 100);
        }

        // 智能预加载下一页
        if (result.data.hasMore && page === 1) {
          this.preloadNextPage();
        }

        return Promise.resolve();

      } else {
        console.error('❌ [消息加载] 加载失败:', result.error);
        wx.showToast({
          title: result.error || '加载失败',
          icon: 'none'
        });
        return Promise.reject(new Error(result.error));
      }

    } catch (error) {
      console.error('❌ [消息加载] 加载异常:', error);
      wx.showToast({
        title: '网络错误',
        icon: 'none'
      });
      return Promise.reject(error);
    } finally {
      // 确保隐藏系统加载提示和页面加载状态
      wx.hideLoading();
      this.setData({ loading: false });
    }
  },

  // 发送消息
  async sendMessage(e) {
    // 检查是否是禁用状态
    if (e && e.currentTarget && e.currentTarget.dataset.disabled === 'true') {
      return;
    }

    const inputText = this.data.inputText.trim();
    if (!inputText) {
      wx.showToast({
        title: '请输入消息内容',
        icon: 'none'
      });
      return;
    }

    try {
      const result = await API.sendMessage(this.data.roomId, inputText, 'text');

      if (result.success) {
        // 清空输入框并重置发送状态，关闭所有面板
        this.setData({
          inputText: '',
          canSend: false,
          showEmojiPanel: false,
          showAttachmentPanel: false
        });

        // 注意：不再手动添加消息到列表，让实时监听器处理
        // 这样可以确保消息的一致性和避免重复

        // 滚动到底部（稍微延迟，等待监听器更新）
        setTimeout(() => {
          this.scrollToBottom();
        }, 100);

        // 清除该聊天室的撤回状态（发送新消息后撤回状态失效）
        getApp().globalData.recalledMessages.clear(this.data.roomId);

        // 通知聊天列表刷新（发送了新消息）
        this.notifyChatListRefresh(true);

      } else {
        wx.showToast({
          title: result.error || '发送失败',
          icon: 'none'
        });
      }

    } catch (error) {
      console.error('❌ [消息发送] 发送异常:', error);
      wx.showToast({
        title: '发送失败',
        icon: 'none'
      });
    }
  },

  // 输入框内容变化
  onInputChange(e) {
    const inputText = e.detail.value;
    const canSend = inputText && inputText.trim().length > 0;

    this.setData({
      inputText: inputText,
      canSend: canSend
    });

    // 当输入内容变化时，延迟滚动到底部确保输入框可见
    setTimeout(() => {
      this.scrollToBottom();
    }, 100);
  },

  // 输入框行数变化
  onLineChange(e) {
    console.log('📝 [输入框] 行数变化:', e.detail);
    // 当行数变化时，滚动到底部确保输入框完全可见
    setTimeout(() => {
      this.scrollToBottom();
    }, 100);
  },

  // 输入框聚焦
  onInputFocus() {
    console.log('📝 [输入框] 聚焦');
    // 聚焦时只关闭附件面板，保持表情包面板打开
    // 这样用户可以在输入文字的同时继续选择表情
    this.setData({
      showAttachmentPanel: false
      // 保持 showEmojiPanel 状态不变，让用户可以继续选择表情
    });
  },

  // 点击消息区域关闭面板
  onMessageAreaTap() {
    if (this.data.showAttachmentPanel || this.data.showEmojiPanel) {
      console.log('📱 [交互] 点击消息区域，关闭面板');
      this.setData({
        showAttachmentPanel: false,
        showEmojiPanel: false
      });

      // 面板关闭后，延迟滚动到底部确保最新消息可见
      setTimeout(() => {
        this.scrollToBottom();
      }, 350); // 等待CSS动画完成
    }
  },

  // 表情包按钮点击
  onEmojiButtonTap() {
    const newShowEmojiPanel = !this.data.showEmojiPanel;
    console.log('🙂 [表情包] 点击表情包按钮，面板状态:', newShowEmojiPanel ? '打开' : '关闭');

    this.setData({
      showEmojiPanel: newShowEmojiPanel,
      showAttachmentPanel: false // 关闭附件面板
    });

    // 面板状态改变后，延迟滚动到底部确保最新消息可见
    setTimeout(() => {
      this.scrollToBottom();
    }, 350); // 等待CSS动画完成
  },

  // 选择表情包
  onSelectEmoji(e) {
    const emoji = e.currentTarget.dataset.emoji;
    console.log('😊 [表情包] 选择表情:', emoji);

    // 将表情添加到输入框
    const currentText = this.data.inputText || '';
    const newText = currentText + emoji;
    const canSend = newText && newText.trim().length > 0;

    this.setData({
      inputText: newText,
      canSend: canSend
    });

    // 保持表情包面板打开，方便用户连续选择表情
    // 用户可以通过以下方式关闭面板：
    // 1. 再次点击表情包按钮
    // 2. 点击输入框聚焦
    // 3. 点击消息区域
    // 4. 发送消息后自动关闭
  },

  // 附件按钮点击
  onAttachmentButtonTap() {
    console.log('📎 [附件] 点击附件按钮');
    this.setData({
      showAttachmentPanel: !this.data.showAttachmentPanel,
      showEmojiPanel: false // 关闭表情包面板
    });

    // 面板状态改变后，延迟滚动到底部确保最新消息可见
    setTimeout(() => {
      this.scrollToBottom();
    }, 350); // 等待CSS动画完成
  },

  // 选择图片（从相册）
  onSelectImage() {
    console.log('🖼️ [附件] 选择图片');
    wx.chooseMedia({
      count: 1,
      mediaType: ['image'],
      sourceType: ['album'], // 只从相册选择
      success: (res) => {
        const tempFilePath = res.tempFiles[0].tempFilePath;
        this.sendImageMessage(tempFilePath);
      },
      fail: (err) => {
        console.error('选择图片失败:', err);
        wx.showToast({
          title: '选择图片失败',
          icon: 'none'
        });
      }
    });

    // 隐藏附件面板
    this.setData({
      showAttachmentPanel: false
    });
  },



  // 拍照
  onSelectCamera() {
    console.log('📷 [附件] 拍照');
    wx.chooseMedia({
      count: 1,
      mediaType: ['image'],
      sourceType: ['camera'], // 只使用相机拍照
      success: (res) => {
        const tempFilePath = res.tempFiles[0].tempFilePath;
        this.sendImageMessage(tempFilePath);
      },
      fail: (err) => {
        console.error('拍照失败:', err);
        wx.showToast({
          title: '拍照失败',
          icon: 'none'
        });
      }
    });

    // 隐藏附件面板
    this.setData({
      showAttachmentPanel: false
    });
  },

  // 发送图片消息
  async sendImageMessage(imagePath) {
    try {
      console.log('🖼️ [图片发送] 开始发送图片:', imagePath);

      wx.showLoading({
        title: '上传中...'
      });

      // 1. 先上传图片到云存储
      const uploadResult = await this.uploadImageToCloud(imagePath);

      if (!uploadResult.success) {
        throw new Error(uploadResult.error || '图片上传失败');
      }

      console.log('🖼️ [图片发送] 图片上传成功:', uploadResult.fileID);

      wx.showLoading({
        title: '发送中...'
      });

      // 2. 发送图片消息
      const result = await API.sendMessage(this.data.roomId, uploadResult.fileID, 'image');

      if (result.success) {
        // 关闭附件面板
        this.setData({
          showAttachmentPanel: false
        });

        // 滚动到底部
        setTimeout(() => {
          this.scrollToBottom();
        }, 100);

        // 通知聊天列表刷新
        this.notifyChatListRefresh(true);

        console.log('✅ [图片发送] 图片消息发送成功');
      } else {
        throw new Error(result.error || '图片消息发送失败');
      }

    } catch (error) {
      console.error('❌ [图片发送] 发送失败:', error);
      wx.showToast({
        title: error.message || '图片发送失败',
        icon: 'none'
      });
    } finally {
      wx.hideLoading();
    }
  },

  // 上传图片到云存储
  async uploadImageToCloud(imagePath) {
    try {
      console.log('☁️ [云存储] 开始上传图片:', imagePath);

      // 生成唯一的文件名
      const timestamp = Date.now();
      const random = Math.random().toString(36).substring(2);
      const fileName = `chat-images/${timestamp}_${random}.jpg`;

      // 上传到云存储
      const uploadResult = await wx.cloud.uploadFile({
        cloudPath: fileName,
        filePath: imagePath
      });

      console.log('☁️ [云存储] 图片上传成功:', uploadResult.fileID);

      return {
        success: true,
        fileID: uploadResult.fileID,
        fileName: fileName
      };

    } catch (error) {
      console.error('❌ [云存储] 图片上传失败:', error);
      return {
        success: false,
        error: error.message || '图片上传失败'
      };
    }
  },

  // 图片点击预览
  onImageTap(e) {
    const { src } = e.currentTarget.dataset;
    console.log('🖼️ [图片预览] 点击图片:', src);

    wx.previewImage({
      current: src,
      urls: [src]
    });
  },

  // 初始化语音录制器
  initVoiceRecorder() {
    console.log('🎤 [语音录制] 初始化录音管理器');
    try {
      this.recorderManager = wx.getRecorderManager();
      this.audioContext = wx.createInnerAudioContext();
      console.log('✅ [语音录制] 录音管理器初始化成功');
      
      // 检查录音权限
      this.checkVoicePermission();
    } catch (error) {
      console.error('❌ [语音录制] 录音管理器初始化失败:', error);
      return;
    }

    // 录音开始事件
    this.recorderManager.onStart(() => {
      console.log('✅ [语音录制] 录音已开始');
      this.setData({ 
        isRecording: true,
        voicePermissionGranted: true 
      });
      this.startRecordTimer();
      
      
    });

    // 录音结束事件
    this.recorderManager.onStop((res) => {
      console.log('🎤 [语音录制] 录音结束:', res);
      console.log('🎤 [语音录制] 当前取消模式状态:', this.data.isCancelMode);
      console.log('🎤 [语音录制] 取消标记状态:', this.isRecordingCancelled);
      
      this.stopRecordTimer();

      // 检查是否是用户主动取消
      if (this.isRecordingCancelled) {
        console.log('🚫 [语音录制] 录音已被用户取消，不处理录音结果');
        this.isRecordingCancelled = false; // 重置取消标记
        this.resetVoiceRecordingState();
        return;
      }

      // 检查是否是取消模式（优先检查，因为这是用户的明确意图）
      if (this.data.isCancelMode) {
        console.log('🚫 [语音录制] 取消模式，不发送录音');
        this.resetVoiceRecordingState();
        
        wx.showToast({
          title: '已取消录音',
          icon: 'none'
        });
        
        
        return;
      }

      // 检查录音时长，太短的录音不发送
      if (res.duration < 1000) { // 少于1秒
        console.log('🎤 [语音录制] 录音时间太短，不发送');
        wx.showToast({
          title: '录音时间太短',
          icon: 'none'
        });
        this.resetVoiceRecordingState();
        
      
        return;
      }

      // 正常情况：设置录音完成状态
      this.setData({
        isRecording: false,
        hasRecording: true,
        currentVoicePath: res.tempFilePath
      });

      // 正常发送录音
      console.log('📤 [语音录制] 正常发送录音');
      console.log('📤 [语音录制] 录音时长(毫秒):', res.duration);
      console.log('📤 [语音录制] 录音时长(秒):', Math.ceil(res.duration / 1000));
      this.sendVoiceMessage(res.tempFilePath, res.duration);
    });

    // 录音错误事件
    this.recorderManager.onError((err) => {
      console.error('❌ [语音录制] 录音失败:', err);
      this.setData({
        isRecording: false,
        hasRecording: false,
        recordTime: 0,
        isCancelMode: false
      });
      this.stopRecordTimer();
      
      // 根据错误类型显示不同提示
      let errorMsg = '录音失败';
      if (err.errMsg && err.errMsg.includes('auth')) {
        errorMsg = '请允许录音权限';
        this.setData({ voicePermissionGranted: false });
      } else if (err.errMsg && err.errMsg.includes('busy')) {
        errorMsg = '录音设备忙碌，请稍后再试';
      }
      
      wx.showToast({
        title: errorMsg,
        icon: 'none'
      });
    });

    // 录音暂停事件
    this.recorderManager.onPause(() => {
      console.log('⏸️ [语音录制] 录音暂停');
    });

    // 录音恢复事件
    this.recorderManager.onResume(() => {
      console.log('▶️ [语音录制] 录音恢复');
    });
  },

  // 检查语音权限
  async checkVoicePermission() {
    try {
      const result = await wx.getSetting();
      const recordAuth = result.authSetting['scope.record'];
      
      if (recordAuth === false) {
        // 用户拒绝了权限
        this.setData({ voicePermissionGranted: false });
        console.log('❌ [语音权限] 用户已拒绝录音权限');
      } else if (recordAuth === true) {
        // 用户已授权
        this.setData({ voicePermissionGranted: true });
        console.log('✅ [语音权限] 用户已授权录音权限');
      } else {
        // 未询问过权限
        console.log('❓ [语音权限] 尚未询问录音权限');
      }
    } catch (error) {
      console.error('❌ [语音权限] 检查权限失败:', error);
    }
  },

  // 请求语音权限
  async requestVoicePermission() {
    try {
      await wx.authorize({
        scope: 'scope.record'
      });
      this.setData({ voicePermissionGranted: true });
      console.log('✅ [语音权限] 权限授权成功');
      return true;
    } catch (error) {
      console.error('❌ [语音权限] 权限授权失败:', error);
      this.setData({ voicePermissionGranted: false });
      
      // 引导用户手动开启权限
      wx.showModal({
        title: '需要录音权限',
        content: '请在设置中开启录音权限以使用语音功能',
        confirmText: '去设置',
        success: (res) => {
          if (res.confirm) {
            wx.openSetting();
          }
        }
      });
      return false;
    }
  },

  // 语音/键盘模式切换
  onVoiceToggle() {
    console.log('🎤 [语音模式] 切换语音模式:', !this.data.isVoiceMode);
    
    // 先关闭所有面板，避免布局冲突
    this.setData({
      showAttachmentPanel: false,
      showEmojiPanel: false
    });
    
    // 延迟切换模式，确保面板关闭动画完成
    setTimeout(() => {
      this.setData({
        isVoiceMode: !this.data.isVoiceMode
      });
    }, 100);
  },

  // 语音按钮触摸开始
  onVoiceTouchStart(e) {
    console.log('🎤 [语音录制] 开始触摸');

    // 防止重复触发
    if (this.data.isRecording) {
      console.log('⚠️ [语音录制] 正在录音中，忽略重复触摸');
      return;
    }

    // 记录触摸开始位置和时间
    this.setData({
      touchStartY: e.touches[0].clientY,
      isCancelMode: false,
      recordingTip: '松开发送',
      recordTime: 0,
      recordTimeDisplay: '0"'
    });

    // 记录触摸开始时间，用于防抖
    this.touchStartTime = Date.now();

    // 开始录音
    this.startVoiceRecord();
  },

  // 语音按钮触摸移动
  onVoiceTouchMove(e) {
    if (!this.data.isRecording) return;

    const currentY = e.touches[0].clientY;
    const deltaY = this.data.touchStartY - currentY;

    // 降低取消阈值，提高灵敏度，上滑超过30px进入取消模式
    const isCancelMode = deltaY > 30;

    if (isCancelMode !== this.data.isCancelMode) {
      console.log('🎤 [语音录制] 切换取消模式:', isCancelMode ? '进入取消模式' : '退出取消模式');
      console.log('🎤 [语音录制] 滑动距离:', deltaY, 'px');
      
      this.setData({ 
        isCancelMode,
        recordingTip: isCancelMode ? '松开取消' : '松开发送'
      });
      
      
    }
  },

  // 语音按钮触摸结束
  onVoiceTouchEnd(e) {
    console.log('🎤 [语音录制] 触摸结束');
    console.log('🎤 [语音录制] 当前状态 - 录音中:', this.data.isRecording, '取消模式:', this.data.isCancelMode);

    // 检查触摸时长，防止误触
    const touchDuration = Date.now() - (this.touchStartTime || 0);
    if (touchDuration < 200) { // 少于200ms认为是误触
      console.log('⚠️ [语音录制] 触摸时间太短，可能是误触');
      if (this.data.isRecording) {
        this.cancelVoiceRecord();
      }
      return;
    }

    // 只有在录音状态下才处理
    if (this.data.isRecording) {
      if (this.data.isCancelMode) {
        // 取消录音 - 用户上滑了
        console.log('🚫 [语音录制] 用户上滑取消录音');
        this.cancelVoiceRecord();
      } else {
        // 正常发送录音 - 用户正常松开
        console.log('📤 [语音录制] 用户正常松开，准备发送录音');
        this.stopAndSendVoiceRecord();
      }
    } else {
      console.log('⚠️ [语音录制] 触摸结束时不在录音状态，忽略');
    }
  },

  // 语音按钮触摸取消
  onVoiceTouchCancel(e) {
    console.log('🎤 [语音录制] 触摸取消');
    if (this.data.isRecording) {
      this.cancelVoiceRecord();
    }
  },

  // 开始录音
  async startVoiceRecord() {
    console.log('🎤 [语音录制] 开始录音');

    // 检查录音权限
    if (!this.data.voicePermissionGranted) {
      const granted = await this.requestVoicePermission();
      if (!granted) {
        return;
      }
    }

    // 检查录音管理器是否已初始化
    if (!this.recorderManager) {
      console.error('❌ [语音录制] 录音管理器未初始化，尝试重新初始化');
      this.initVoiceRecorder();
      if (!this.recorderManager) {
        wx.showToast({
          title: '录音功能不可用',
          icon: 'none'
        });
        return;
      }
    }

    // 重置录音状态
    this.setData({
      recordTime: 0,
      isRecording: false,
      hasRecording: false,
      currentVoicePath: '',
      isCancelMode: false
    });

    console.log('🎤 [语音录制] 准备开始录音...');

    // 开始录音
    try {
      this.recorderManager.start({
        duration: 60000, // 最长60秒
        sampleRate: 16000,
        numberOfChannels: 1,
        encodeBitRate: 96000,
        format: 'mp3',
        frameSize: 50 // 帧大小，影响onFrameRecorded回调频率
      });
      console.log('🎤 [语音录制] 录音命令已发送');
    } catch (error) {
      console.error('❌ [语音录制] 启动录音失败:', error);
      wx.showToast({
        title: '录音启动失败: ' + error.message,
        icon: 'none'
      });
    }
  },

  // 停止并发送录音
  stopAndSendVoiceRecord() {
    console.log('🎤 [语音录制] 停止并发送录音');

    if (this.data.isRecording && this.recorderManager) {
      this.recorderManager.stop();
    }
  },

  // 取消录音
  cancelVoiceRecord() {
    console.log('🎤 [语音录制] 取消录音');
    console.log('🎤 [语音录制] 当前录音状态:', this.data.isRecording);

    // 设置取消标记，防止在录音停止事件中自动发送
    this.isRecordingCancelled = true;

    // 如果正在录音，停止录音
    if (this.data.isRecording && this.recorderManager) {
      try {
        console.log('🎤 [语音录制] 停止录音管理器');
        this.recorderManager.stop();
      } catch (error) {
        console.error('❌ [语音录制] 停止录音失败:', error);
        // 即使停止失败，也要重置状态
        this.resetVoiceRecordingState();
        this.isRecordingCancelled = false;
      }
    } else {
      // 如果没有在录音，直接重置状态
      console.log('🎤 [语音录制] 没有在录音，直接重置状态');
      this.resetVoiceRecordingState();
      this.isRecordingCancelled = false;
      
      // 显示取消提示
      wx.showToast({
        title: '已取消录音',
        icon: 'none',
        duration: 1500
      });

      
    }
  },

  // 发送语音消息
  async sendVoiceMessage(voicePath, duration) {
    console.log('🎤 [语音发送] 开始发送语音消息');
    console.log('🎤 [语音发送] 语音文件路径:', voicePath);
    console.log('🎤 [语音发送] 原始时长(毫秒):', duration);

    if (!voicePath) {
      console.error('❌ [语音发送] 语音文件路径为空');
      return;
    }

    // 确保时长是有效的数字
    const durationInSeconds = Math.max(1, Math.ceil((duration || 0) / 1000));
    console.log('🎤 [语音发送] 转换后时长(秒):', durationInSeconds);

    try {
      // 显示上传中提示
      wx.showLoading({
        title: '发送中...',
        mask: true
      });

      // 上传语音文件到云存储
      const uploadResult = await this.uploadVoiceToCloud(voicePath, duration);

      if (!uploadResult.success) {
        throw new Error(uploadResult.error || '语音上传失败');
      }

      console.log('✅ [语音发送] 语音文件上传成功:', uploadResult.fileID);

      // 发送语音消息，使用API统一接口，确保时长正确传递
      const result = await API.sendVoiceMessage(
        this.data.roomId, 
        uploadResult.fileID, 
        durationInSeconds // 使用处理后的秒数
      );
      
      console.log('🎤 [语音发送] API调用参数:', {
        roomId: this.data.roomId,
        fileID: uploadResult.fileID,
        duration: durationInSeconds
      });

      if (result.success) {
        console.log('✅ [语音发送] 语音消息发送成功');

        // 重置语音状态
        this.setData({
          hasRecording: false,
          currentVoicePath: '',
          recordTime: 0,
          isRecording: false,
          isCancelMode: false
        });

        // 关闭附件面板
        this.setData({
          showAttachmentPanel: false
        });

        // 滚动到底部
        setTimeout(() => {
          this.scrollToBottom();
        }, 100);

        // 通知聊天列表刷新
        this.notifyChatListRefresh(true);

        

      } else {
        throw new Error(result.error || '发送失败');
      }

    } catch (error) {
      console.error('❌ [语音发送] 发送语音消息失败:', error);
      wx.showToast({
        title: error.message || '发送失败',
        icon: 'none'
      });
    } finally {
      wx.hideLoading();
    }
  },

  // 开始录音
  onStartRecord() {
    console.log('🎤 [语音录制] 用户点击开始录音');

    // 检查录音管理器是否已初始化
    if (!this.recorderManager) {
      console.error('❌ [语音录制] 录音管理器未初始化，尝试重新初始化');
      this.initVoiceRecorder();
      if (!this.recorderManager) {
        wx.showToast({
          title: '录音功能不可用',
          icon: 'none'
        });
        return;
      }
    }

    // 重置录音状态
    this.setData({
      recordTime: 0,
      isRecording: false,
      hasRecording: false
    });

    console.log('🎤 [语音录制] 准备开始录音...');

    // 直接开始录音，微信会自动处理权限
    try {
      this.recorderManager.start({
        duration: 60000, // 最长60秒
        sampleRate: 16000,
        numberOfChannels: 1,
        encodeBitRate: 96000,
        format: 'mp3'
      });
      console.log('🎤 [语音录制] 录音命令已发送');
    } catch (error) {
      console.error('❌ [语音录制] 启动录音失败:', error);
      wx.showToast({
        title: '录音启动失败: ' + error.message,
        icon: 'none'
      });
    }
  },



  // 取消录音
  onCancelVoiceRecord() {
    console.log('🎤 [语音录制] 取消录音');
    if (this.data.isRecording) {
      this.recorderManager.stop();
    }
    this.setData({
      showVoicePanel: false,
      isRecording: false,
      hasRecording: false,
      recordTime: 0,
      currentVoicePath: ''
    });
    this.stopRecordTimer();
  },

  // 发送语音消息
  async onSendVoiceRecord() {
    if (!this.data.hasRecording || !this.data.currentVoicePath) {
      wx.showToast({
        title: '请先录制语音',
        icon: 'none'
      });
      return;
    }

    try {
      console.log('🎤 [语音发送] 开始发送语音:', this.data.currentVoicePath);

      wx.showLoading({
        title: '发送中...'
      });

      // 上传语音到云存储
      const uploadResult = await this.uploadVoiceToCloud(this.data.currentVoicePath, this.data.recordTime);

      if (!uploadResult.success) {
        throw new Error(uploadResult.error || '语音上传失败');
      }

      // 发送语音消息，需要传递时长信息
      const result = await API.sendVoiceMessage(this.data.roomId, uploadResult.fileID, this.data.recordTime);

      if (result.success) {
        // 关闭语音面板
        this.setData({
          showVoicePanel: false,
          isRecording: false,
          hasRecording: false,
          recordTime: 0,
          currentVoicePath: ''
        });

        // 滚动到底部
        setTimeout(() => {
          this.scrollToBottom();
        }, 100);

        // 通知聊天列表刷新
        this.notifyChatListRefresh(true);

        console.log('✅ [语音发送] 语音消息发送成功');
      } else {
        throw new Error(result.error || '语音消息发送失败');
      }

    } catch (error) {
      console.error('❌ [语音发送] 发送失败:', error);
      wx.showToast({
        title: error.message || '语音发送失败',
        icon: 'none'
      });
    } finally {
      wx.hideLoading();
    }
  },

  // 上传语音到云存储
  async uploadVoiceToCloud(voicePath, duration) {
    try {
      console.log('☁️ [云存储] 开始上传语音:', voicePath);
      console.log('☁️ [云存储] 语音时长(毫秒):', duration);

      // 生成唯一的文件名，包含时长信息
      const timestamp = Date.now();
      const random = Math.random().toString(36).substring(2);
      const durationInMs = duration || 0;
      const fileName = `chat-voices/${timestamp}_${random}_duration${durationInMs}.mp3`;

      console.log('☁️ [云存储] 生成文件名:', fileName);

      // 上传到云存储
      const uploadResult = await wx.cloud.uploadFile({
        cloudPath: fileName,
        filePath: voicePath
      });

      console.log('☁️ [云存储] 语音上传成功:', uploadResult.fileID);

      return {
        success: true,
        fileID: uploadResult.fileID,
        fileName: fileName,
        duration: duration // 保持原始毫秒数，在发送时再转换
      };

    } catch (error) {
      console.error('❌ [云存储] 语音上传失败:', error);
      return {
        success: false,
        error: error.message || '语音上传失败'
      };
    }
  },

  // 开始录音计时
  startRecordTimer() {
    console.log('⏰ [录音计时] 开始计时');
    this.recordTimer = setInterval(() => {
      const newTime = this.data.recordTime + 1;
      const timeDisplay = this.formatRecordTime(newTime);
      console.log('⏰ [录音计时] 当前时长:', newTime, '显示:', timeDisplay);
      
      this.setData({ 
        recordTime: newTime,
        recordTimeDisplay: timeDisplay
      });

      // 最长60秒自动停止
      if (newTime >= 60) {
        console.log('⏰ [录音计时] 达到最大时长，自动停止');
        this.stopAndSendVoiceRecord();
      }
    }, 1000);
  },

  // 停止录音计时
  stopRecordTimer() {
    if (this.recordTimer) {
      clearInterval(this.recordTimer);
      this.recordTimer = null;
    }
  },

  // 播放语音消息
  onVoicePlay(e) {
    const { src, messageId } = e.currentTarget.dataset;
    console.log('🎵 [语音播放] 点击播放语音:', src);

    // 如果当前消息正在播放，则停止播放
    const currentMessage = this.data.messageList.find(msg => msg._id === messageId);
    if (currentMessage && currentMessage.isPlaying) {
      console.log('🎵 [语音播放] 停止当前播放');
      this.stopCurrentVoice();
      return;
    }

    // 停止其他正在播放的语音
    this.stopCurrentVoice();

    // 设置当前消息为播放状态
    this.setVoicePlayingState(messageId, true);

    // 创建新的音频上下文用于播放
    const playAudioContext = wx.createInnerAudioContext();
    playAudioContext.src = src;
    playAudioContext.autoplay = true;
    playAudioContext.volume = 1.0; // 设置音量

    // 监听播放开始
    playAudioContext.onPlay(() => {
      console.log('🎵 [语音播放] 开始播放');
      
    });

    // 监听播放结束
    playAudioContext.onEnded(() => {
      console.log('🎵 [语音播放] 播放结束');
      this.setVoicePlayingState(messageId, false);
      this.cleanupAudioContext(playAudioContext);
    });

    // 监听播放错误
    playAudioContext.onError((err) => {
      console.error('❌ [语音播放] 播放失败:', err);
      this.setVoicePlayingState(messageId, false);
      this.cleanupAudioContext(playAudioContext);
      
      wx.showToast({
        title: '语音播放失败',
        icon: 'none'
      });
    });

    // 监听播放停止
    playAudioContext.onStop(() => {
      console.log('🎵 [语音播放] 播放停止');
      this.setVoicePlayingState(messageId, false);
      this.cleanupAudioContext(playAudioContext);
    });

    // 监听播放暂停
    playAudioContext.onPause(() => {
      console.log('⏸️ [语音播放] 播放暂停');
      this.setVoicePlayingState(messageId, false);
    });

    // 保存当前播放的音频上下文
    this.currentPlayingAudio = playAudioContext;
  },

  // 清理音频上下文
  cleanupAudioContext(audioContext) {
    if (audioContext) {
      try {
        audioContext.destroy();
      } catch (error) {
        console.error('❌ [语音播放] 清理音频上下文失败:', error);
      }
    }
    
    // 清除引用
    if (this.currentPlayingAudio === audioContext) {
      this.currentPlayingAudio = null;
    }
  },

  // 停止当前播放的语音
  stopCurrentVoice() {
    if (this.currentPlayingAudio) {
      try {
        this.currentPlayingAudio.stop();
        this.cleanupAudioContext(this.currentPlayingAudio);
      } catch (error) {
        console.error('❌ [语音播放] 停止播放失败:', error);
      }
    }

    // 清除所有消息的播放状态
    const messageList = this.data.messageList.map(msg => ({
      ...msg,
      isPlaying: false
    }));

    this.setData({ messageList });
  },

  // 设置语音播放状态
  setVoicePlayingState(messageId, isPlaying) {
    const messageList = this.data.messageList.map(msg => ({
      ...msg,
      isPlaying: msg._id === messageId ? isPlaying : false
    }));

    this.setData({ messageList });
  },

  // 滚动到底部
  scrollToBottom() {
    console.log('📍 [滚动] 开始滚动到底部，消息总数:', this.data.messageList.length);

    // 强制滚动方法：使用时间戳确保每次都是不同的值
    const forceScrollToBottom = () => {
      const scrollTop = Date.now() + Math.random() * 1000000; // 确保每次都不同
      this.setData({
        scrollTop: scrollTop,
        scrollToView: ''
      });
      console.log('📍 [滚动] 强制滚动，scrollTop:', scrollTop);
    };

    // 立即执行一次
    forceScrollToBottom();

    // 延迟执行多次，确保滚动生效
    setTimeout(forceScrollToBottom, 100);
    setTimeout(forceScrollToBottom, 300);
    setTimeout(forceScrollToBottom, 500);

    // 最后使用scroll-into-view作为备用
    setTimeout(() => {
      this.setData({
        scrollToView: 'bottom-anchor'
      });
      console.log('📍 [滚动] 备用方案scrollToView: bottom-anchor');
    }, 700);
  },

  // 滚动事件
  onScroll(e) {
    const { scrollTop } = e.detail;

    // 滚动到顶部时加载更多历史消息
    if (scrollTop <= 50 && this.data.hasMore && !this.data.loading) {
      console.log('📥 [历史消息] 加载更多历史消息');
      this.setData({
        page: this.data.page + 1
      });
      this.loadMessages(false);
    }
  },

  // 通知聊天列表刷新
  notifyChatListRefresh(hasNewMessage = false, forceRefresh = false) {
    // 如果不是新消息且不是强制刷新，跳过
    if (!hasNewMessage && !forceRefresh) {
      console.log('🔄 [列表刷新] 无新消息且非强制刷新，跳过刷新');
      return;
    }

    console.log('🔄 [列表刷新] 开始通知聊天列表刷新');

    try {
      const pages = getCurrentPages();
      console.log('🔄 [列表刷新] 当前页面栈:', pages.map(p => p.route));

      const chatListPage = pages.find(page =>
        page.route === 'pages/chat/list/list'
      );

      if (chatListPage) {
        console.log('🔄 [列表刷新] 找到聊天列表页面，检查方法:', {
          hasLoadChatListSilently: typeof chatListPage.loadChatListSilently === 'function',
          hasForceRefresh: typeof chatListPage.forceRefresh === 'function'
        });

        if (chatListPage.loadChatListSilently) {
          console.log('🔄 [列表刷新] 调用静默刷新方法');
          setTimeout(() => {
            chatListPage.loadChatListSilently();
          }, 1000); // 增加延迟，确保聊天室lastMessage字段完全更新
        } else if (chatListPage.forceRefresh) {
          console.log('🔄 [列表刷新] 调用强制刷新方法');
          setTimeout(() => {
            chatListPage.forceRefresh();
          }, 1000);
        } else {
          console.log('🔄 [列表刷新] 聊天列表页面没有可用的刷新方法');
        }
      } else {
        // 如果找不到聊天列表页面，设置全局标记
        console.log('🔄 [列表刷新] 未找到聊天列表页面，设置全局刷新标记');
        getApp().globalData.needRefreshChatList = true;
      }

      // 额外保险：总是设置全局标记
      console.log('🔄 [列表刷新] 设置全局刷新标记作为备用');
      getApp().globalData.needRefreshChatList = true;

    } catch (error) {
      console.error('❌ [列表刷新] 通知失败:', error);
      // 出错时也设置全局标记
      getApp().globalData.needRefreshChatList = true;
    }
  },

  // 查看订单详情
  viewOrderDetail() {
    if (this.data.orderNo) {
      wx.navigateTo({
        url: `/order-package/pages/detail/detail?id=${this.data.orderNo}`
      });
    } else {
      wx.showToast({
        title: '订单信息不存在',
        icon: 'none'
      });
    }
  },

  // 页面分享
  onShareAppMessage() {
    return {
      title: '聊天室',
      path: `/chat-package/pages/room/room?roomId=${this.data.roomId}&orderNo=${this.data.orderNo}`
    };
  },

  // 启动消息监听器
  startMessageWatcher() {
    if (!this.data.roomId || this.data.isWatcherActive) {
      console.log('⚠️ [实时监听] 监听器已启动或房间ID无效，跳过启动');
      return;
    }

    try {
      const db = wx.cloud.database();

      // 监听当前聊天室的消息变更
      const watcher = db.collection('messages')
        .where({
          chatRoomId: this.data.roomId
        })
        .orderBy('createTime', 'desc')
        .limit(50) // 限制监听最新50条消息
        .watch({
          onChange: (snapshot) => {
            // 确保页面仍然存在且监听器仍然活跃
            if (this.data && this.data.isWatcherActive) {
              this.handleMessageChange(snapshot);
            }
          },
          onError: (error) => {
            console.error('监听器错误:', error);

            // 更新状态
            if (this.data) {
              this.setData({
                isWatcherActive: false,
                messageWatcher: null
              });
            }

            // 监听失败时，启用轮询模式作为备用方案
            this.startPollingMode();
          }
        });

      // 保存监听器引用并更新状态
      this.setData({
        messageWatcher: watcher,
        isWatcherActive: true
      });

    } catch (error) {
      console.error('❌ [实时监听] 启动监听器失败:', error);
      this.setData({
        isWatcherActive: false,
        messageWatcher: null
      });
    }
  },

  // 停止消息监听器
  stopMessageWatcher() {
    if (!this.data) {
      return; // 页面已销毁
    }

    if (this.data.messageWatcher || this.data.isWatcherActive) {
      console.log('🛑 [实时监听] 停止消息监听器');

      try {
        // 关闭监听器
        if (this.data.messageWatcher && typeof this.data.messageWatcher.close === 'function') {
          this.data.messageWatcher.close();
        }

        // 清理状态
        this.setData({
          messageWatcher: null,
          isWatcherActive: false
        });

        console.log('✅ [实时监听] 监听器已停止');
      } catch (error) {
        console.error('❌ [实时监听] 停止监听器失败:', error);

        // 即使出错也要清理状态
        try {
          this.setData({
            messageWatcher: null,
            isWatcherActive: false
          });
        } catch (setDataError) {
          console.error('❌ [实时监听] 清理状态失败:', setDataError);
        }
      }
    }
  },

  // 处理消息变更事件
  handleMessageChange(snapshot) {
    console.log('📨 [实时监听] 收到消息变更:', snapshot);

    // 如果是初始化数据，不处理（避免与loadMessages冲突）
    if (snapshot.type === 'init') {
      console.log('📋 [实时监听] 跳过初始化数据');
      return;
    }

    // 处理消息变更（新增和更新）
    const newMessages = [];
    const updatedMessages = [];

    snapshot.docChanges.forEach(change => {
      console.log('🔍 [实时监听] 处理变更:', {
        queueType: change.queueType,
        dataType: change.dataType,
        messageId: change.doc._id,
        isRecalled: change.doc.isRecalled
      });

      console.log('🔍 [实时监听] 开始处理变更:', change.queueType);

      if (change.queueType === 'enqueue' || change.queueType === 'update') {
        const messageData = change.doc;
        console.log('🔍 [实时监听] 消息原始数据:', {
          _id: messageData._id,
          content: messageData.content,
          isRecalled: messageData.isRecalled,
          type: messageData.type
        });

        // 格式化消息数据
        const formattedMessage = {
          _id: messageData._id,
          chatRoomId: messageData.chatRoomId,
          senderId: messageData.senderId,
          senderInfo: messageData.senderInfo,
          content: messageData.content,
          type: messageData.type,
          createTime: messageData.createTime,
          formattedTime: this.formatMessageTime(messageData.createTime),
          isSelf: messageData.senderId === (this.data.userInfo?._id || ''),
          isRecalled: messageData.isRecalled || false,
          canRecall: this.canRecallMessage(messageData)
        };
        
        // 特别处理语音消息的时长
        if (messageData.type === 'voice') {
          console.log('🎵 [实时监听] 处理语音消息时长:', {
            messageId: messageData._id,
            originalDuration: messageData.duration,
            durationType: typeof messageData.duration
          });
          
          // 确保时长是数字类型
          if (messageData.duration && typeof messageData.duration === 'number') {
            formattedMessage.duration = messageData.duration;
            console.log('🎵 [实时监听] 使用数据库时长:', messageData.duration);
          } else if (messageData.duration && typeof messageData.duration === 'string') {
            formattedMessage.duration = parseInt(messageData.duration) || 0;
            console.log('🎵 [实时监听] 转换字符串时长:', formattedMessage.duration);
          } else {
            console.warn('⚠️ [实时监听] 语音消息时长数据异常:', messageData.duration);
            // 尝试从文件名中提取时长信息
            if (messageData.content && typeof messageData.content === 'string') {
              console.log('🔍 [实时监听] 尝试从URL提取时长:', messageData.content);
              
              // 尝试匹配新格式：duration数字.mp3
              let durationMatch = messageData.content.match(/duration(\d+)\.mp3/);
              if (durationMatch) {
                const extractedDuration = Math.ceil(parseInt(durationMatch[1]) / 1000);
                console.log('🎵 [实时监听] 从新格式文件名提取时长:', extractedDuration, '秒');
                formattedMessage.duration = extractedDuration;
              } else {
                // 尝试匹配旧格式：durationTime=数字
                durationMatch = messageData.content.match(/durationTime=(\d+)/);
                if (durationMatch) {
                  const extractedDuration = Math.ceil(parseInt(durationMatch[1]) / 1000);
                  console.log('🎵 [实时监听] 从旧格式文件名提取时长:', extractedDuration, '秒');
                  formattedMessage.duration = extractedDuration;
                } else {
                  console.log('🎵 [实时监听] 无法从文件名提取时长，设置为0');
                  formattedMessage.duration = 0;
                }
              }
            } else {
              console.log('🎵 [实时监听] 内容为空，设置时长为0');
              formattedMessage.duration = 0;
            }
          }
          
          console.log('🎵 [实时监听] 处理后时长:', formattedMessage.duration);
        }

        console.log('🔍 [实时监听] 格式化后消息:', {
          _id: formattedMessage._id,
          content: formattedMessage.content,
          isRecalled: formattedMessage.isRecalled,
          type: formattedMessage.type
        });

        // 处理撤回消息的显示
        if (formattedMessage.isRecalled) {
          console.log('🔍 [实时监听] 处理撤回消息显示:', messageData._id);
          formattedMessage.content = '[消息已撤回]';
          formattedMessage.type = 'recalled';
          console.log('🔍 [实时监听] 撤回消息处理完成:', formattedMessage.content);
        }

        // 检查消息是否已存在
        const existingIndex = this.data.messageList.findIndex(msg => msg._id === messageData._id);

        console.log('🔍 [实时监听] 消息处理判断:', {
          dataType: change.dataType,
          existingIndex: existingIndex,
          messageId: messageData._id,
          isRecalled: formattedMessage.isRecalled
        });

        if (change.dataType === 'update') {
          // 消息更新（包括撤回）
          console.log('🔄 [实时监听] 准备添加更新消息:', formattedMessage._id, formattedMessage.isRecalled);
          updatedMessages.push(formattedMessage);
          console.log('🔄 [实时监听] 消息更新已添加:', formattedMessage._id, formattedMessage.isRecalled ? '已撤回' : '更新');
          console.log('🔄 [实时监听] 当前updatedMessages长度:', updatedMessages.length);
        } else if (change.dataType === 'add' && existingIndex === -1) {
          // 新消息
          newMessages.push(formattedMessage);
          console.log('📨 [实时监听] 新消息:', formattedMessage.content);
        } else if (existingIndex !== -1) {
          // 已存在的消息，当作更新处理
          updatedMessages.push(formattedMessage);
          console.log('🔄 [实时监听] 已存在消息更新:', formattedMessage._id);
        } else {
          // 兜底处理：如果dataType不明确，根据是否存在来判断
          if (existingIndex === -1) {
            newMessages.push(formattedMessage);
            console.log('📨 [实时监听] 新消息(兜底):', formattedMessage.content);
          } else {
            updatedMessages.push(formattedMessage);
            console.log('🔄 [实时监听] 消息更新(兜底):', formattedMessage._id);
          }
        }
      } else {
        console.log('⚠️ [实时监听] 跳过不支持的变更类型:', change.queueType);
      }
    });

    // 处理消息更新
    let updatedMessageList = [...this.data.messageList];

    // 处理更新的消息（如撤回）
    console.log('🔍 [实时监听] 准备处理更新消息，数量:', updatedMessages.length);
    if (updatedMessages.length > 0) {
      updatedMessages.forEach(updatedMsg => {
        console.log('🔍 [实时监听] 处理更新消息:', updatedMsg._id, '撤回状态:', updatedMsg.isRecalled);
        const existingIndex = updatedMessageList.findIndex(msg => msg._id === updatedMsg._id);
        console.log('🔍 [实时监听] 在本地列表中的位置:', existingIndex);
        if (existingIndex !== -1) {
          // 更新现有消息
          console.log('🔍 [实时监听] 更新前:', updatedMessageList[existingIndex].content, '撤回状态:', updatedMessageList[existingIndex].isRecalled);
          updatedMessageList[existingIndex] = updatedMsg;
          console.log('🔍 [实时监听] 更新后:', updatedMessageList[existingIndex].content, '撤回状态:', updatedMessageList[existingIndex].isRecalled);
          console.log('✅ [实时监听] 消息已更新:', updatedMsg._id);
        } else {
          console.log('⚠️ [实时监听] 未找到要更新的消息:', updatedMsg._id);
        }
      });
    }

    // 如果有新消息，添加到列表并滚动到底部
    if (newMessages.length > 0) {
      // 按时间排序新消息
      newMessages.sort((a, b) => new Date(a.createTime) - new Date(b.createTime));

      newMessages.forEach(newMsg => {
        // 找到插入位置（按时间顺序）
        let insertIndex = updatedMessageList.length;
        for (let i = updatedMessageList.length - 1; i >= 0; i--) {
          if (new Date(updatedMessageList[i].createTime) <= new Date(newMsg.createTime)) {
            insertIndex = i + 1;
            break;
          }
        }
        updatedMessageList.splice(insertIndex, 0, newMsg);
      });

      // 通知聊天列表刷新（有新消息）
      this.notifyChatListRefresh(true);

      console.log(`✅ [实时监听] 添加了 ${newMessages.length} 条新消息`);
    }

    // 如果有任何变更（新消息或更新），更新UI
    if (newMessages.length > 0 || updatedMessages.length > 0) {
      this.setData({
        messageList: updatedMessageList
      });

      // 如果有新消息，滚动到底部
      if (newMessages.length > 0) {
        // 延迟滚动到底部，确保DOM更新完成
        setTimeout(() => {
          this.scrollToBottom();
        }, 100);

        // 再次延迟滚动，确保滚动到最新消息
        setTimeout(() => {
          this.scrollToBottom();
        }, 300);
      }

      // 如果有消息更新（如撤回），通知聊天列表刷新
      if (updatedMessages.length > 0) {
        // 检查是否有撤回消息
        const hasRecalledMessage = updatedMessages.some(msg => msg.isRecalled);
        if (hasRecalledMessage) {
          // 撤回消息需要强制刷新聊天列表
          console.log('🔄 [实时监听] 检测到撤回消息，强制刷新聊天列表');
          this.notifyChatListRefresh(false, true); // 强制刷新
        } else {
          // 其他更新可能是新消息
          this.notifyChatListRefresh(true);
        }
        console.log(`✅ [实时监听] 更新了 ${updatedMessages.length} 条消息`);
      }
    }
  },

  // 页面卸载
  onUnload() {
    console.log('📱 [页面状态] 页面卸载，清理资源');

    // 停止消息监听器
    this.stopMessageWatcher();

    // 清除活跃聊天室状态
    app.globalData.setActiveChatRoom(null);

    // 停止轮询模式
    this.stopPollingMode();

    // 清理语音相关资源
    this.cleanupVoiceResources();

    // 清理所有引用，防止内存泄漏
    this.data.messageWatcher = null;
    this.data.messageList = [];
  },

  // 清理语音相关资源
  cleanupVoiceResources() {
    console.log('🧹 [资源清理] 清理语音相关资源');

    // 停止录音计时器
    this.stopRecordTimer();

    // 停止当前录音
    if (this.recorderManager && this.data.isRecording) {
      try {
        this.recorderManager.stop();
      } catch (error) {
        console.error('❌ [资源清理] 停止录音失败:', error);
      }
    }

    // 清理录音管理器
    if (this.recorderManager) {
      try {
        // 移除所有事件监听器 - 使用更安全的方式
        if (typeof this.recorderManager.off === 'function') {
          this.recorderManager.off('start');
          this.recorderManager.off('stop');
          this.recorderManager.off('error');
          this.recorderManager.off('pause');
          this.recorderManager.off('resume');
        }
      } catch (error) {
        console.error('🚨 [资源清理] 清理录音管理器事件监听失败:', error);
      }
      this.recorderManager = null;
    }

    // 清理音频上下文
    if (this.audioContext) {
      try {
        this.audioContext.destroy();
      } catch (error) {
        console.error('❌ [资源清理] 清理音频上下文失败:', error);
      }
      this.audioContext = null;
    }

    // 停止当前播放的语音
    if (this.currentPlayingAudio) {
      this.cleanupAudioContext(this.currentPlayingAudio);
    }

    console.log('✅ [资源清理] 语音资源清理完成');
  },

  // 判断消息是否可以撤回
  canRecallMessage(message) {
    if (!message || !this.data.userInfo) {
      return false;
    }

    // 只能撤回自己发送的消息
    if (message.senderId !== this.data.userInfo._id) {
      return false;
    }

    // 已撤回的消息不能再次撤回
    if (message.isRecalled) {
      return false;
    }

    // 检查时间限制：2分钟内可撤回
    const now = new Date();
    const messageTime = new Date(message.createTime);
    const timeDiff = (now - messageTime) / 1000 / 60; // 分钟

    return timeDiff <= 2;
  },

  // 消息长按事件
  onMessageLongPress(e) {
    const { messageId, messageIndex, isSelf, isRecalled, createTime } = e.currentTarget.dataset;

    console.log('📱 [消息操作] 长按消息:', { messageId, messageIndex, isSelf, isRecalled });

    // 只有自己发送的消息才能操作
    if (!isSelf) {
      return;
    }

    // 已撤回的消息不能再操作
    if (isRecalled) {
      return;
    }

    // 检查是否可以撤回
    const message = this.data.messageList[messageIndex];
    const canRecall = this.canRecallMessage(message);

    const itemList = [];
    if (canRecall) {
      itemList.push('撤回消息');
    }
    itemList.push('复制消息');

    if (itemList.length === 0) {
      wx.showToast({
        title: '暂无可用操作',
        icon: 'none'
      });
      return;
    }

    wx.showActionSheet({
      itemList: itemList,
      success: (res) => {
        const selectedAction = itemList[res.tapIndex];

        if (selectedAction === '撤回消息') {
          this.recallMessage(messageId, messageIndex);
        } else if (selectedAction === '复制消息') {
          this.copyMessage(message);
        }
      },
      fail: (err) => {
        console.log('用户取消操作');
      }
    });
  },

  // 撤回消息
  async recallMessage(messageId, messageIndex) {
    try {
      wx.showLoading({
        title: '撤回中...',
        mask: true
      });

      console.log('🔄 [消息撤回] 开始撤回消息:', messageId);
      console.log('🔄 [消息撤回] 聊天室ID:', this.data.roomId);

      const result = await API.recallMessage(messageId, this.data.roomId);

      console.log('🔄 [消息撤回] 云函数返回结果:', result);

      if (result.success) {
        console.log('🎯 [消息撤回] 开始处理撤回成功逻辑');

        try {
          // 更新本地消息状态
          console.log('🔄 [消息撤回] 更新本地消息状态');
          const messageList = [...this.data.messageList];
          if (messageList[messageIndex]) {
            messageList[messageIndex] = {
              ...messageList[messageIndex],
              isRecalled: true,
              content: '[消息已撤回]',
              type: 'recalled',
              canRecall: false
            };

            this.setData({
              messageList: messageList
            });
            console.log('✅ [消息撤回] 本地消息状态已更新');
          }

          wx.showToast({
            title: '撤回成功',
            icon: 'success'
          });

          console.log('✅ [消息撤回] 撤回成功');

          // 获取被撤回消息的时间
          console.log('🔄 [消息撤回] 获取被撤回消息时间');
          const recalledMessage = this.data.messageList[messageIndex];
          const messageTime = recalledMessage ? recalledMessage.createTime : null;
          console.log('🔄 [消息撤回] 消息时间:', messageTime);

          // 使用全局状态管理标记撤回（这会自动触发聊天列表更新）
          console.log('🔄 [消息撤回] 标记全局撤回状态');
          getApp().globalData.recalledMessages.markAsRecalled(this.data.roomId, '[消息已撤回]', messageTime);

          // 强制刷新聊天列表以确保撤回状态正确显示
          console.log('🔄 [消息撤回] 强制刷新聊天列表');
          this.notifyChatListRefresh(false, true);

          console.log('✅ [消息撤回] 撤回成功处理完成');
        } catch (error) {
          console.error('❌ [消息撤回] 撤回成功处理异常:', error);
          throw error;
        }

      } else {
        wx.showToast({
          title: result.error || '撤回失败',
          icon: 'none'
        });
        console.error('❌ [消息撤回] 撤回失败:', result.error);
      }

    } catch (error) {
      console.error('❌ [消息撤回] 撤回异常:', error);
      wx.showToast({
        title: '撤回失败',
        icon: 'none'
      });
    } finally {
      wx.hideLoading();
    }
  },

  // 复制消息
  copyMessage(message) {
    if (!message || !message.content) {
      wx.showToast({
        title: '无法复制此消息',
        icon: 'none'
      });
      return;
    }

    wx.setClipboardData({
      data: message.content,
      success: () => {
        wx.showToast({
          title: '复制成功',
          icon: 'success'
        });
      },
      fail: () => {
        wx.showToast({
          title: '复制失败',
          icon: 'none'
        });
      }
    });
  },

  // 消息缓存相关方法
  getMessageCache(key) {
    try {
      if (!this.messageCache) {
        this.messageCache = new Map();
      }
      return this.messageCache.get(key);
    } catch (error) {
      console.error('📦 [消息缓存] 获取缓存失败:', error);
      return null;
    }
  },

  setMessageCache(key, messages) {
    try {
      if (!this.messageCache) {
        this.messageCache = new Map();
      }

      // 限制缓存大小
      if (this.messageCache.size >= 10) {
        const firstKey = this.messageCache.keys().next().value;
        this.messageCache.delete(firstKey);
      }

      this.messageCache.set(key, {
        messages: messages,
        timestamp: Date.now()
      });

      console.log('📦 [消息缓存] 缓存已保存:', key);
    } catch (error) {
      console.error('📦 [消息缓存] 保存缓存失败:', error);
    }
  },

  processCachedMessages(cachedData, page) {
    const { messages } = cachedData;
    const messageList = [...messages, ...this.data.messageList];

    this.setData({
      messageList: messageList,
      page: page,
      loading: false
    });
  },

  clearOldMessageCache() {
    try {
      if (!this.messageCache) return;

      const now = Date.now();
      const maxAge = 5 * 60 * 1000; // 5分钟

      for (const [key, value] of this.messageCache.entries()) {
        if (now - value.timestamp > maxAge) {
          this.messageCache.delete(key);
        }
      }

      console.log('📦 [消息缓存] 清理过期缓存完成');
    } catch (error) {
      console.error('📦 [消息缓存] 清理缓存失败:', error);
    }
  },

  // 智能预加载下一页
  preloadNextPage() {
    if (this.preloadTimer) {
      clearTimeout(this.preloadTimer);
    }

    // 延迟2秒预加载
    this.preloadTimer = setTimeout(() => {
      if (this.data.hasMore && !this.data.loading) {
        console.log('🚀 [智能预加载] 预加载下一页消息');
        this.setData({
          page: this.data.page + 1
        });
        this.loadMessages(false);
      }
    }, 2000);
  },

  // 初始化图片优化器
  initImageOptimizer() {
    try {
      // 清理过期缓存
      imageOptimizer.clearExpiredCache();

      // 预加载聊天室中的图片
      this.preloadChatImages();

      console.log('🖼️ [图片优化] 初始化完成');
    } catch (error) {
      console.error('🖼️ [图片优化] 初始化失败:', error);
    }
  },

  // 预加载聊天室图片
  async preloadChatImages() {
    try {
      // 获取最近的图片消息
      const imageMessages = this.data.messageList
        .filter(msg => msg.type === 'image' && msg.content)
        .slice(0, 10); // 只预加载最近10张图片

      if (imageMessages.length > 0) {
        const imageUrls = imageMessages.map(msg => msg.content);
        console.log('🚀 [图片预加载] 开始预加载聊天图片:', imageUrls.length);

        // 异步预加载，不阻塞主流程
        imageOptimizer.preloadImages(imageUrls).catch(error => {
          console.error('🖼️ [图片预加载] 失败:', error);
        });
      }
    } catch (error) {
      console.error('🖼️ [图片预加载] 异常:', error);
    }
  },

  // 优化图片显示
  async optimizeImageDisplay(imageUrl) {
    try {
      // 检查缓存
      const cachedImage = imageOptimizer.getCacheImage(imageUrl);
      if (cachedImage) {
        return cachedImage;
      }

      // 智能压缩图片
      const optimizedImage = await imageOptimizer.smartCompress(imageUrl);
      return optimizedImage;
    } catch (error) {
      console.error('🖼️ [图片优化] 优化失败:', error);
      return imageUrl; // 返回原图
    }
  },

  // 处理图片消息发送
  async handleImageMessage(imagePath) {
    try {
      wx.showLoading({
        title: '处理图片中...',
        mask: true
      });

      // 智能压缩图片
      const optimizedPath = await imageOptimizer.smartCompress(imagePath);

      console.log('🖼️ [图片处理] 压缩完成:', {
        original: imagePath,
        optimized: optimizedPath
      });

      // 发送压缩后的图片
      await this.sendMessage(optimizedPath, 'image');

      wx.hideLoading();
    } catch (error) {
      console.error('🖼️ [图片处理] 处理失败:', error);
      wx.hideLoading();

      // 处理失败时发送原图
      try {
        await this.sendMessage(imagePath, 'image');
      } catch (sendError) {
        wx.showToast({
          title: '图片发送失败',
          icon: 'none'
        });
      }
    }
  },

  // 强制刷新聊天列表（用于撤回消息等重要更新）
  forceRefreshChatList() {
    console.log('🔄 [强制刷新] 开始强制刷新聊天列表');

    try {
      const pages = getCurrentPages();
      const chatListPage = pages.find(page => page.route === 'pages/chat/list/list');

      if (chatListPage) {
        console.log('🔄 [强制刷新] 找到聊天列表页面，执行强制刷新');

        // 优先使用强制刷新方法
        if (chatListPage.forceRefresh) {
          setTimeout(() => {
            chatListPage.forceRefresh();
          }, 500);
        } else if (chatListPage.loadChatListSilently) {
          setTimeout(() => {
            chatListPage.loadChatListSilently();
          }, 500);
        } else if (chatListPage.loadChatList) {
          setTimeout(() => {
            chatListPage.loadChatList();
          }, 500);
        }
      } else {
        console.log('🔄 [强制刷新] 未找到聊天列表页面');
      }

      // 设置全局刷新标记
      getApp().globalData.needRefreshChatList = true;

    } catch (error) {
      console.error('❌ [强制刷新] 刷新失败:', error);
      getApp().globalData.needRefreshChatList = true;
    }
  },

  // 直接更新聊天列表中的最后消息显示
  updateChatListLastMessage(newContent) {
    console.log('🔄 [直接更新] 更新聊天列表最后消息:', newContent);
    console.log('🔄 [直接更新] 当前聊天室ID:', this.data.roomId);

    try {
      const pages = getCurrentPages();
      console.log('🔄 [直接更新] 当前页面栈:', pages.map(p => p.route));

      const chatListPage = pages.find(page => page.route === 'pages/chat/list/list');

      if (chatListPage) {
        console.log('🔄 [直接更新] 找到聊天列表页面');
        console.log('🔄 [直接更新] 聊天列表数据存在:', !!chatListPage.data?.chatList);
        console.log('🔄 [直接更新] 聊天列表长度:', chatListPage.data?.chatList?.length || 0);

        if (chatListPage.data && chatListPage.data.chatList) {
          const chatList = [...chatListPage.data.chatList];
          console.log('🔄 [直接更新] 开始查找当前聊天室:', this.data.roomId);

          const currentRoomIndex = chatList.findIndex(chat => {
            console.log('🔍 [直接更新] 检查聊天室:', chat._id, '是否匹配:', chat._id === this.data.roomId);
            return chat._id === this.data.roomId;
          });

          console.log('🔄 [直接更新] 聊天室索引:', currentRoomIndex);

          if (currentRoomIndex !== -1) {
            console.log('🔄 [直接更新] 找到当前聊天室，更新最后消息');
            console.log('🔄 [直接更新] 更新前:', chatList[currentRoomIndex].lastMessageDisplay);

            // 更新显示内容
            chatList[currentRoomIndex].lastMessageDisplay = newContent;

            // 更新最后消息对象
            if (chatList[currentRoomIndex].lastMessage) {
              chatList[currentRoomIndex].lastMessage = {
                ...chatList[currentRoomIndex].lastMessage,
                content: newContent,
                type: 'recalled'
              };
            } else {
              chatList[currentRoomIndex].lastMessage = {
                content: newContent,
                type: 'recalled',
                createTime: new Date()
              };
            }

            console.log('🔄 [直接更新] 更新后:', chatList[currentRoomIndex].lastMessageDisplay);

            // 更新页面数据
            chatListPage.setData({
              chatList: chatList
            });

            console.log('✅ [直接更新] 聊天列表最后消息已更新');
          } else {
            console.log('⚠️ [直接更新] 未找到当前聊天室');
            console.log('🔍 [直接更新] 所有聊天室ID:', chatList.map(chat => chat._id));
          }
        } else {
          console.log('⚠️ [直接更新] 聊天列表数据不存在');
        }
      } else {
        console.log('⚠️ [直接更新] 未找到聊天列表页面');
      }

    } catch (error) {
      console.error('❌ [直接更新] 更新失败:', error);
    }
  },

  // 专门针对撤回消息的强制更新（绕过智能合并）
  forceUpdateChatListForRecall(recallContent) {
    console.log('🚀 [撤回专用] 强制更新聊天列表为撤回状态:', recallContent);

    try {
      const pages = getCurrentPages();
      const chatListPage = pages.find(page => page.route === 'pages/chat/list/list');

      if (chatListPage && chatListPage.data && chatListPage.data.chatList) {
        const chatList = [...chatListPage.data.chatList];
        const currentRoomIndex = chatList.findIndex(chat => chat._id === this.data.roomId);

        if (currentRoomIndex !== -1) {
          console.log('🚀 [撤回专用] 找到目标聊天室，强制覆盖');

          // 强制覆盖所有相关字段
          chatList[currentRoomIndex].lastMessageDisplay = recallContent;
          chatList[currentRoomIndex].lastMessage = {
            content: recallContent,
            type: 'recalled',
            createTime: new Date(),
            messageId: 'recalled_' + Date.now()
          };

          // 设置特殊标记，防止被智能合并覆盖
          chatList[currentRoomIndex]._forceRecalled = true;
          chatList[currentRoomIndex]._recallTime = Date.now();

          console.log('🚀 [撤回专用] 更新前:', chatList[currentRoomIndex].lastMessageDisplay);

          // 立即更新页面数据
          chatListPage.setData({
            chatList: chatList
          });

          console.log('✅ [撤回专用] 强制更新完成');

          // 额外保险：直接操作DOM（如果setData不生效）
          setTimeout(() => {
            this.forceUpdateDOMForRecall(currentRoomIndex, recallContent);
          }, 100);

        } else {
          console.log('⚠️ [撤回专用] 未找到目标聊天室');
        }
      } else {
        console.log('⚠️ [撤回专用] 未找到聊天列表页面');
      }

    } catch (error) {
      console.error('❌ [撤回专用] 强制更新失败:', error);
    }
  },

  // DOM级别的强制更新（最后手段）
  forceUpdateDOMForRecall(roomIndex, recallContent) {
    try {
      console.log('🔧 [DOM更新] 尝试直接操作DOM');
      const pages = getCurrentPages();
      const chatListPage = pages.find(page => page.route === 'pages/chat/list/list');

      if (chatListPage && chatListPage.selectComponent) {
        // 尝试通过组件选择器更新
        const chatItemComponent = chatListPage.selectComponent(`#chat-item-${roomIndex}`);
        if (chatItemComponent) {
          chatItemComponent.setData({
            'item.lastMessageDisplay': recallContent
          });
          console.log('✅ [DOM更新] 组件更新成功');
        }
      }
    } catch (error) {
      console.log('⚠️ [DOM更新] DOM操作失败:', error);
    }
  },

  // 立即更新聊天列表为撤回状态（不等待任何异步操作）
  immediateUpdateChatListForRecall(recallContent) {
    console.log('⚡ [立即更新] 立即更新聊天列表为撤回状态:', recallContent);

    try {
      const pages = getCurrentPages();
      const chatListPage = pages.find(page => page.route === 'pages/chat/list/list');

      if (chatListPage && chatListPage.data && chatListPage.data.chatList) {
        const chatList = [...chatListPage.data.chatList];
        const currentRoomIndex = chatList.findIndex(chat => chat._id === this.data.roomId);

        if (currentRoomIndex !== -1) {
          console.log('⚡ [立即更新] 找到目标聊天室，立即更新');

          // 完全替换该聊天室的消息信息
          chatList[currentRoomIndex].lastMessageDisplay = recallContent;
          chatList[currentRoomIndex].lastMessage = {
            content: recallContent,
            type: 'recalled',
            createTime: new Date(),
            messageId: 'recalled_' + Date.now()
          };

          // 立即更新页面数据
          chatListPage.setData({
            chatList: chatList
          });

          console.log('✅ [立即更新] 立即更新完成');
        } else {
          console.log('⚠️ [立即更新] 未找到目标聊天室');
        }
      } else {
        console.log('⚠️ [立即更新] 未找到聊天列表页面');
      }

    } catch (error) {
      console.error('❌ [立即更新] 立即更新失败:', error);
    }
  },

  // 设置永久撤回标记
  setPermanentRecallMark(recallContent) {
    console.log('🔒 [永久标记] 设置永久撤回标记');

    // 在本地存储中保存撤回标记
    try {
      wx.setStorageSync(`recall_${this.data.roomId}`, {
        content: recallContent,
        timestamp: Date.now()
      });

      // 设置全局标记
      if (!getApp().globalData.permanentRecallRooms) {
        getApp().globalData.permanentRecallRooms = {};
      }
      getApp().globalData.permanentRecallRooms[this.data.roomId] = {
        content: recallContent,
        timestamp: Date.now()
      };

      console.log('✅ [永久标记] 永久撤回标记已设置');

    } catch (error) {
      console.error('❌ [永久标记] 设置永久标记失败:', error);
    }
  },

  // 启动轮询模式（实时监听器失败时的备用方案）
  startPollingMode() {
    if (this.pollingTimer) {
      clearInterval(this.pollingTimer);
    }

    console.log('🔄 [轮询模式] 启动消息轮询检测');

    // 记录最后检查的时间
    this.lastPollingTime = new Date();

    // 每3秒检查一次消息更新
    this.pollingTimer = setInterval(() => {
      if (this.data && this.data.roomId) {
        this.checkMessageUpdates();
      } else {
        // 页面已销毁，停止轮询
        this.stopPollingMode();
      }
    }, 3000);
  },

  // 停止轮询模式
  stopPollingMode() {
    if (this.pollingTimer) {
      clearInterval(this.pollingTimer);
      this.pollingTimer = null;
      console.log('⏹️ [轮询模式] 已停止轮询');
    }
  },

  // 检查消息更新
  async checkMessageUpdates() {
    try {
      // 查询最近更新的消息
      const result = await API.callFunction('chatMessage', {
        action: 'get',
        chatRoomId: this.data.roomId,
        page: 1,
        pageSize: 10
      }, { showLoading: false });

      if (result.success && result.data.list.length > 0) {
        // 检查是否有消息状态变化（如撤回）
        this.handlePollingUpdates(result.data.list);
      }

    } catch (error) {
      console.error('❌ [轮询模式] 检查更新失败:', error);
    }
  },

  // 处理轮询检测到的消息更新
  handlePollingUpdates(serverMessages) {
    let hasUpdates = false;
    let updatedMessageList = [...this.data.messageList];

    // 检查服务器消息与本地消息的差异
    serverMessages.forEach(serverMsg => {
      const localIndex = updatedMessageList.findIndex(msg => msg._id === serverMsg._id);

      if (localIndex !== -1) {
        const localMsg = updatedMessageList[localIndex];

        // 检查撤回状态是否发生变化
        if (localMsg.isRecalled !== serverMsg.isRecalled) {
          console.log('🔄 [轮询模式] 检测到消息状态变化:', serverMsg._id, serverMsg.isRecalled ? '已撤回' : '恢复');

          // 更新消息状态
          const updatedMsg = {
            ...localMsg,
            isRecalled: serverMsg.isRecalled || false,
            content: serverMsg.isRecalled ? '[消息已撤回]' : serverMsg.content,
            type: serverMsg.isRecalled ? 'recalled' : serverMsg.type,
            canRecall: this.canRecallMessage(serverMsg)
          };

          updatedMessageList[localIndex] = updatedMsg;
          hasUpdates = true;
        }
      }
    });

    // 如果有更新，刷新UI
    if (hasUpdates) {
      console.log('✅ [轮询模式] 更新UI，发现消息状态变化');
      this.setData({
        messageList: updatedMessageList
      });

      // 通知聊天列表刷新
      this.notifyChatListRefresh(true);
    }
  },

  // 格式化语音时长显示
  formatVoiceDuration(duration) {
    if (!duration || duration <= 0) {
      return '0"';
    }
    
    const seconds = Math.ceil(duration);
    if (seconds < 60) {
      return `${seconds}"`;
    } else {
      const minutes = Math.floor(seconds / 60);
      const remainingSeconds = seconds % 60;
      return `${minutes}'${remainingSeconds.toString().padStart(2, '0')}"`;
    }
  },

  // 检查语音文件大小
  checkVoiceFileSize(filePath) {
    return new Promise((resolve) => {
      wx.getFileInfo({
        filePath: filePath,
        success: (res) => {
          const sizeInMB = res.size / (1024 * 1024);
          console.log('🎤 [语音检查] 文件大小:', sizeInMB.toFixed(2), 'MB');
          
          // 限制语音文件大小为10MB
          if (sizeInMB > 10) {
            resolve({
              valid: false,
              error: '语音文件过大，请重新录制'
            });
          } else {
            resolve({
              valid: true,
              size: res.size
            });
          }
        },
        fail: (error) => {
          console.error('❌ [语音检查] 获取文件信息失败:', error);
          resolve({
            valid: false,
            error: '无法获取语音文件信息'
          });
        }
      });
    });
  },

  // 语音质量检测
  checkVoiceQuality(duration, fileSize) {
    const durationInSeconds = duration / 1000;
    const sizeInKB = fileSize / 1024;
    const bitrate = (sizeInKB * 8) / durationInSeconds; // kbps

    console.log('🎤 [语音质量] 时长:', durationInSeconds.toFixed(1), 's, 大小:', sizeInKB.toFixed(1), 'KB, 码率:', bitrate.toFixed(1), 'kbps');

    // 检查码率是否合理（一般在32-128kbps之间）
    if (bitrate < 16) {
      return {
        quality: 'low',
        warning: '语音质量较低，可能影响播放效果'
      };
    } else if (bitrate > 256) {
      return {
        quality: 'high',
        warning: '语音文件较大，上传可能较慢'
      };
    } else {
      return {
        quality: 'good',
        warning: null
      };
    }
  },

  // 语音录制状态重置
  resetVoiceRecordingState() {
    console.log('🔄 [状态重置] 重置语音录制状态');
    
    this.setData({
      isRecording: false,
      hasRecording: false,
      recordTime: 0,
      recordTimeDisplay: '0"',
      currentVoicePath: '',
      isCancelMode: false,
      recordingTip: '松开发送'
    });
    
    this.stopRecordTimer();
    this.touchStartTime = null;
    
    // 重置取消标记
    this.isRecordingCancelled = false;
    
    console.log('✅ [状态重置] 语音录制状态已重置');
  },

  // 格式化录音时长显示
  formatRecordTime(seconds) {
    if (!seconds || seconds <= 0) {
      return '0"';
    }
    
    if (seconds < 60) {
      return `${seconds}"`;
    } else {
      const minutes = Math.floor(seconds / 60);
      const remainingSeconds = seconds % 60;
      return `${minutes}'${remainingSeconds.toString().padStart(2, '0')}"`;
    }
  }

});
