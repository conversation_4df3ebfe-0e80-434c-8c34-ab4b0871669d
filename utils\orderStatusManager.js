// 订单状态管理器
class OrderStatusManager {
  constructor() {
    this.statusMap = {
      'pending': '待接单',
      'accepted': '已接单',
      'in_progress': '进行中',
      'completed': '已完成',
      'cancelled': '已取消'
    };
  }

  // 获取订单状态文本
  getStatusText(status) {
    return this.statusMap[status] || '未知状态';
  }

  // 检查状态是否为活跃状态（进行中）
  isActiveStatus(status) {
    return ['accepted', 'in_progress'].includes(status);
  }

  // 检查状态是否为完成状态
  isCompletedStatus(status) {
    return status === 'completed';
  }

  // 检查状态是否为取消状态
  isCancelledStatus(status) {
    return status === 'cancelled';
  }

  // 获取今日订单统计
  async getTodayOrderStats() {
    try {
      const result = await wx.cloud.callFunction({
        name: 'getOrderStatistics',
        data: {
          action: 'getTodayStats'
        }
      });

      if (result.result && result.result.success) {
        return result.result.data;
      }

      return {
        total: 0,
        pending: 0,
        accepted: 0,
        in_progress: 0,
        completed: 0,
        cancelled: 0
      };
    } catch (error) {
      console.error('❌ [订单统计] 获取今日订单统计失败:', error);
      return {
        total: 0,
        pending: 0,
        accepted: 0,
        in_progress: 0,
        completed: 0,
        cancelled: 0
      };
    }
  }

  // 更新订单状态
  async updateOrderStatus(orderId, newStatus, updateData = {}) {
    try {
      console.log('🔄 [订单状态] 更新订单状态:', { orderId, newStatus, updateData });

      const result = await wx.cloud.callFunction({
        name: 'updateOrderStatus',
        data: {
          orderId: orderId,
          status: newStatus,
          updateData: updateData,
          updateTime: new Date()
        }
      });

      if (result.result && result.result.success) {
        console.log('✅ [订单状态] 订单状态更新成功');
        
        // 触发全局事件
        const app = getApp();
        app.$emit('orderStatusUpdated', {
          orderId: orderId,
          oldStatus: updateData.oldStatus,
          newStatus: newStatus,
          updateTime: new Date()
        });

        return result.result;
      } else {
        console.error('❌ [订单状态] 订单状态更新失败:', result.result?.error);
        return {
          success: false,
          error: result.result?.error || '更新失败'
        };
      }
    } catch (error) {
      console.error('❌ [订单状态] 更新订单状态异常:', error);
      return {
        success: false,
        error: error.message || '更新异常'
      };
    }
  }

  // 接单操作
  async acceptOrder(orderId) {
    return await this.updateOrderStatus(orderId, 'accepted', {
      acceptTime: new Date()
    });
  }

  // 开始订单操作
  async startOrder(orderId) {
    return await this.updateOrderStatus(orderId, 'in_progress', {
      startTime: new Date()
    });
  }

  // 完成订单操作
  async completeOrder(orderId) {
    return await this.updateOrderStatus(orderId, 'completed', {
      completeTime: new Date()
    });
  }

  // 取消订单操作
  async cancelOrder(orderId, reason = '') {
    return await this.updateOrderStatus(orderId, 'cancelled', {
      cancelTime: new Date(),
      cancelReason: reason
    });
  }

  // 获取用户的活跃订单数量
  async getUserActiveOrderCount(userId) {
    try {
      const result = await wx.cloud.callFunction({
        name: 'getOrderList',
        data: {
          action: 'getActiveCount',
          userId: userId
        }
      });

      if (result.result && result.result.success) {
        return result.result.data.count || 0;
      }

      return 0;
    } catch (error) {
      console.error('❌ [订单统计] 获取用户活跃订单数量失败:', error);
      return 0;
    }
  }
}

// 创建全局实例
const orderStatusManager = new OrderStatusManager();

module.exports = orderStatusManager;
