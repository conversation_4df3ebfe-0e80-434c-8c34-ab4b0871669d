// 常量定义

const SERVICE_TYPES = {
  GAMING: 'gaming',
  COACHING: 'coaching',
  BOOST: 'boost'
};

const SERVICE_TYPE_NAMES = {
  [SERVICE_TYPES.GAMING]: '游戏服务',
  [SERVICE_TYPES.COACHING]: '教学',
  [SERVICE_TYPES.BOOST]: '代练'
};

const ORDER_STATUS = {
  PENDING: 'pending',
  ACCEPTED: 'accepted',
  IN_PROGRESS: 'in_progress',
  COMPLETED: 'completed',
  CANCELLED: 'cancelled',
  DISPUTED: 'disputed'
};

const ORDER_STATUS_NAMES = {
  [ORDER_STATUS.PENDING]: '待接单',
  [ORDER_STATUS.ACCEPTED]: '已接单',
  [ORDER_STATUS.IN_PROGRESS]: '进行中',
  [ORDER_STATUS.COMPLETED]: '已完成',
  [ORDER_STATUS.CANCELLED]: '已取消',
  [ORDER_STATUS.DISPUTED]: '有争议'
};

const ACCEPTOR_LEVELS = {
  NOVICE: 'novice',
  PROFESSIONAL: 'professional',
  MASTER: 'master'
};

const ACCEPTOR_LEVEL_NAMES = {
  [ACCEPTOR_LEVELS.NOVICE]: '新手接单者',
  [ACCEPTOR_LEVELS.PROFESSIONAL]: '专业接单者',
  [ACCEPTOR_LEVELS.MASTER]: '大师级接单者'
};

const ACCEPTOR_STATUS = {
  ONLINE: 'online',
  OFFLINE: 'offline',
  BUSY: 'busy'
};

const ACCEPTOR_STATUS_NAMES = {
  [ACCEPTOR_STATUS.ONLINE]: '在线',
  [ACCEPTOR_STATUS.OFFLINE]: '离线',
  [ACCEPTOR_STATUS.BUSY]: '忙碌'
};

const SKILL_TYPES = {
  SHOOTING: 'shooting',
  TACTICS: 'tactics',
  TEAMWORK: 'teamwork',
  COMMUNICATION: 'communication'
};

const SKILL_TYPE_NAMES = {
  [SKILL_TYPES.SHOOTING]: '射击技巧',
  [SKILL_TYPES.TACTICS]: '战术意识',
  [SKILL_TYPES.TEAMWORK]: '团队配合',
  [SKILL_TYPES.COMMUNICATION]: '沟通能力'
};

const PAYMENT_STATUS = {
  PENDING: 'pending',
  PAID: 'paid',
  REFUNDED: 'refunded',
  FAILED: 'failed'
};

const PAYMENT_STATUS_NAMES = {
  [PAYMENT_STATUS.PENDING]: '待支付',
  [PAYMENT_STATUS.PAID]: '已支付',
  [PAYMENT_STATUS.REFUNDED]: '已退款',
  [PAYMENT_STATUS.FAILED]: '支付失败'
};

const MESSAGE_TYPES = {
  TEXT: 'text',
  IMAGE: 'image',
  VOICE: 'voice',
  SYSTEM: 'system'
};

const TRANSACTION_TYPES = {
  RECHARGE: 'recharge',
  PAYMENT: 'payment',
  EARNINGS: 'earnings',
  WITHDRAW: 'withdraw',
  REFUND: 'refund'
};

const TRANSACTION_TYPE_NAMES = {
  [TRANSACTION_TYPES.RECHARGE]: '充值',
  [TRANSACTION_TYPES.PAYMENT]: '支付',
  [TRANSACTION_TYPES.EARNINGS]: '收入',
  [TRANSACTION_TYPES.WITHDRAW]: '提现',
  [TRANSACTION_TYPES.REFUND]: '退款'
};

// 默认配置
const DEFAULT_CONFIG = {
  PAGE_SIZE: 20,
  MAX_UPLOAD_SIZE: 10 * 1024 * 1024, // 10MB
  PLATFORM_FEE_RATE: 0.1, // 平台费率 10%
  MIN_WITHDRAW_AMOUNT: 10, // 最小提现金额
  MAX_HOURLY_RATE: 200, // 最大时薪
  MIN_HOURLY_RATE: 10 // 最小时薪
};

// 导出所有常量
module.exports = {
  SERVICE_TYPES,
  SERVICE_TYPE_NAMES,
  ORDER_STATUS,
  ORDER_STATUS_NAMES,
  ACCEPTOR_LEVELS,
  ACCEPTOR_LEVEL_NAMES,
  ACCEPTOR_STATUS,
  ACCEPTOR_STATUS_NAMES,
  SKILL_TYPES,
  SKILL_TYPE_NAMES,
  PAYMENT_STATUS,
  PAYMENT_STATUS_NAMES,
  MESSAGE_TYPES,
  TRANSACTION_TYPES,
  TRANSACTION_TYPE_NAMES,
  DEFAULT_CONFIG
};
