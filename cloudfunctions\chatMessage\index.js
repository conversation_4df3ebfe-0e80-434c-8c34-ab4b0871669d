/**
 * 聊天消息管理云函数
 * 功能：发送消息、获取消息列表
 */
const cloud = require('wx-server-sdk');

cloud.init({
  env: cloud.DYNAMIC_CURRENT_ENV
});

const db = cloud.database();

// 工具函数：验证用户是否有权限在聊天室发送消息
async function validateChatRoomPermission(chatRoomId, userId) {
  console.log('🔍 [权限验证] 验证聊天室权限:', chatRoomId, userId);
  
  try {
    const chatRoomResult = await db.collection('chatRooms').doc(chatRoomId).get();
    
    if (!chatRoomResult.data) {
      return { valid: false, error: '聊天室不存在' };
    }
    
    const chatRoom = chatRoomResult.data;
    
    // 检查用户是否是聊天室的参与者
    const isCustomer = chatRoom.customerId === userId;
    const isAccepter = chatRoom.accepterId === userId;
    
    if (!isCustomer && !isAccepter) {
      return { valid: false, error: '您没有权限在此聊天室发送消息' };
    }
    
    const userRole = isCustomer ? 'customer' : 'accepter';
    
    console.log('✅ [权限验证] 验证通过，用户角色:', userRole);
    
    return { 
      valid: true, 
      chatRoom: chatRoom,
      userRole: userRole
    };
  } catch (error) {
    console.error('❌ [权限验证] 验证失败:', error);
    return { valid: false, error: '权限验证失败' };
  }
}



// 发送消息
async function sendMessage(chatRoomId, content, type, user, extra = {}) {
  // 验证用户权限
  const permissionResult = await validateChatRoomPermission(chatRoomId, user._id);
  if (!permissionResult.valid) {
    throw new Error(permissionResult.error);
  }

  const chatRoom = permissionResult.chatRoom;
  const userRole = permissionResult.userRole;

  // 创建消息数据
  const messageData = {
    chatRoomId: chatRoomId,
    senderId: user._id,
    senderInfo: {
      nickName: user.nickName || '未知用户',
      avatarUrl: user.avatarUrl || ''
    },
    content: type === 'text' ? content.trim() : content,
    type: type,
    extra: extra,
    createTime: new Date()
  };

  // 为语音消息添加时长信息
  if (type === 'voice' && extra.duration) {
    messageData.duration = extra.duration;
  }

  // 保存消息到数据库
  const messageResult = await db.collection('messages').add({
    data: messageData
  });

  // 更新聊天室最后消息信息
  messageData._id = messageResult._id;

  try {
    await cloud.callFunction({
      name: 'updateChatRoomLastMessage',
      data: {
        chatRoomId: chatRoomId,
        messageData: messageData
      }
    });
  } catch (error) {
    console.error('❌ [消息发送] 更新聊天室lastMessage失败:', error);
  }

  return {
    messageId: messageResult._id,
    message: messageData,
    chatRoom: {
      id: chatRoomId,
      orderNo: chatRoom.orderNo,
      userRole: userRole
    }
  };
}

// 获取消息列表
async function getMessages(chatRoomId, page, pageSize, user) {
  console.log('📥 [消息查询] 开始查询消息列表');
  
  // 验证用户权限
  const permissionResult = await validateChatRoomPermission(chatRoomId, user._id);
  if (!permissionResult.valid) {
    throw new Error(permissionResult.error);
  }

  // 查询消息列表（性能优化版）
  let messagesResult;
  try {
    // 优化查询：只获取必要字段，减少数据传输量
    messagesResult = await db.collection('messages')
      .where({ chatRoomId: chatRoomId })
      .field({
        _id: true,
        chatRoomId: true,
        senderId: true,
        senderInfo: true, // 包含发送者信息（头像和昵称）
        content: true,
        type: true,
        createTime: true,
        isRecalled: true,
        recallTime: true,
        // 排除大字段以提高性能
        originalContent: false,
        extra: false
      })
      .orderBy('createTime', 'desc')
      .skip((page - 1) * pageSize)
      .limit(pageSize)
      .get();
  } catch (orderError) {

    // 备用查询方案
    messagesResult = await db.collection('messages')
      .where({ chatRoomId: chatRoomId })
      .field({
        _id: true,
        chatRoomId: true,
        senderId: true,
        senderInfo: true, // 包含发送者信息（头像和昵称）
        content: true,
        type: true,
        createTime: true,
        isRecalled: true,
        recallTime: true
      })
      .skip((page - 1) * pageSize)
      .limit(pageSize)
      .get();
  }

  console.log(`✅ [消息查询] 找到消息数量: ${messagesResult.data.length}`);

  // 格式化消息数据（性能优化版）
  const messages = messagesResult.data.map(message => {
    // 基础消息数据
    const formattedMessage = {
      _id: message._id,
      chatRoomId: message.chatRoomId,
      senderId: message.senderId,
      senderInfo: message.senderInfo || { nickName: '未知用户', avatarUrl: '' }, // 确保包含发送者信息
      content: message.content,
      type: message.type,
      createTime: message.createTime,
      isSelf: message.senderId === user._id
    };

    // 为语音消息添加时长信息
    if (message.type === 'voice' && message.duration) {
      formattedMessage.duration = message.duration;
    }

    // 只在需要时添加撤回相关字段
    if (message.isRecalled) {
      formattedMessage.isRecalled = true;
      if (message.recallTime) {
        formattedMessage.recallTime = message.recallTime;
      }
    }

    return formattedMessage;
  });

  // 如果查询结果是倒序的，需要反转数组以显示正确的时间顺序
  messages.reverse();

  return {
    list: messages,
    page: page,
    pageSize: pageSize,
    hasMore: messagesResult.data.length === pageSize,
    // 添加性能统计信息
    performance: {
      queryTime: Date.now(),
      messageCount: messages.length,
      optimized: true
    }
  };
}

// 撤回消息
async function recallMessage(messageId, user, chatRoomId) {

  try {
    // 查询消息
    const messageResult = await db.collection('messages').doc(messageId).get();

    if (!messageResult.data) {
      throw new Error('消息不存在');
    }

    const message = messageResult.data;

    // 验证权限：只有发送者可以撤回消息
    if (message.senderId !== user._id) {
      throw new Error('只能撤回自己发送的消息');
    }

    // 检查时间限制：2分钟内可撤回
    const now = new Date();
    const messageTime = new Date(message.createTime);
    const timeDiff = (now - messageTime) / 1000 / 60; // 分钟

    if (timeDiff > 2) {
      throw new Error('消息发送超过2分钟，无法撤回');
    }

    // 验证聊天室权限
    const permissionResult = await validateChatRoomPermission(chatRoomId, user._id);
    if (!permissionResult.valid) {
      throw new Error(permissionResult.error);
    }

    // 验证消息是否属于指定的聊天室
    if (message.chatRoomId !== chatRoomId) {
      throw new Error('消息不属于指定的聊天室');
    }

    // 更新消息状态为已撤回
    await db.collection('messages').doc(messageId).update({
      data: {
        isRecalled: true,
        recallTime: now,
        originalContent: message.content, // 保存原始内容用于审计
        content: '[消息已撤回]',
        type: 'recalled'
      }
    });

    // 更新聊天室的最后消息（撤回后的处理）
    await updateChatRoomLastMessageAfterRecall(chatRoomId, messageId);



    return {
      messageId: messageId,
      recallTime: now
    };

  } catch (error) {
    console.error('❌ [消息撤回] 撤回失败:', error);
    throw error;
  }
}

// 撤回后更新聊天室最后消息
async function updateChatRoomLastMessageAfterRecall(chatRoomId, recalledMessageId) {
  try {


    // 获取聊天室信息
    const chatRoomResult = await db.collection('chatRooms').doc(chatRoomId).get();
    if (!chatRoomResult.data) {
      return;
    }

    const chatRoom = chatRoomResult.data;

    // 详细调试聊天室lastMessage结构
    console.log('🔍 [撤回后更新] 聊天室完整lastMessage:', JSON.stringify(chatRoom.lastMessage, null, 2));

    console.log('🔍 [撤回后更新] 聊天室当前状态:', {
      chatRoomId: chatRoomId,
      hasLastMessage: !!chatRoom.lastMessage,
      lastMessageId: chatRoom.lastMessage?.messageId || chatRoom.lastMessage?._id,
      lastMessageContent: chatRoom.lastMessage?.content,
      lastMessageSenderId: chatRoom.lastMessage?.senderId,
      recalledMessageId: recalledMessageId
    });

    // 检查被撤回的消息是否是最后一条消息（支持多种ID字段）
    const lastMessageId = chatRoom.lastMessage?.messageId || chatRoom.lastMessage?._id;
    if (chatRoom.lastMessage && lastMessageId === recalledMessageId) {


      // 直接将最后消息更新为撤回状态
      const updatedLastMessage = {
        ...chatRoom.lastMessage,
        content: '[消息已撤回]',
        type: 'recalled',
        isRecalled: true
      };

      await db.collection('chatRooms').doc(chatRoomId).update({
        data: {
          lastMessage: updatedLastMessage,
          updateTime: new Date()
        }
      });

    }

  } catch (error) {
    console.error('❌ [撤回后更新] 更新失败:', error);
    // 不抛出错误，避免影响撤回操作
  }
}

// 更新聊天室最后消息（原函数保留，用于其他场景）
async function updateChatRoomLastMessage(chatRoomId, recalledMessageId) {
  try {
    console.log('🔄 [聊天室更新] 检查是否需要更新最后消息');

    // 获取聊天室信息
    const chatRoomResult = await db.collection('chatRooms').doc(chatRoomId).get();
    if (!chatRoomResult.data) {
      console.log('⚠️ [聊天室更新] 聊天室不存在');
      return;
    }

    const chatRoom = chatRoomResult.data;

    console.log('🔍 [聊天室更新] 聊天室当前状态:', {
      chatRoomId: chatRoomId,
      hasLastMessage: !!chatRoom.lastMessage,
      lastMessageId: chatRoom.lastMessage?.messageId,
      recalledMessageId: recalledMessageId
    });

    // 检查被撤回的消息是否是最后一条消息
    if (chatRoom.lastMessage && chatRoom.lastMessage.messageId === recalledMessageId) {
      // 查找最新的未撤回消息

      // 直接使用简单查询，避免复合索引问题
      const allMessagesResult = await db.collection('messages')
        .where({
          chatRoomId: chatRoomId
        })
        .get();

      // 手动过滤和排序
      const unrecalledMessages = allMessagesResult.data
        .filter(msg => {
          const isRecalled = msg.isRecalled === true;
          return !isRecalled;
        })
        .sort((a, b) => new Date(b.createTime) - new Date(a.createTime));

      const latestMessageResult = {
        data: unrecalledMessages.slice(0, 1)
      };

      let newLastMessage = null;

      if (latestMessageResult.data.length > 0) {
        const latestMessage = latestMessageResult.data[0];
        newLastMessage = {
          messageId: latestMessage._id,
          content: latestMessage.content,
          type: latestMessage.type,
          senderId: latestMessage.senderId,
          createTime: latestMessage.createTime
        };
        console.log('📋 [聊天室更新] 新的最后消息:', newLastMessage.content);
      } else {
        console.log('📋 [聊天室更新] 没有找到未撤回的消息，设置为空');
      }

      // 更新聊天室的最后消息
      await db.collection('chatRooms').doc(chatRoomId).update({
        data: {
          lastMessage: newLastMessage,
          updateTime: new Date()
        }
      });

      console.log('✅ [聊天室更新] 最后消息已更新:', newLastMessage ? newLastMessage.content : '空');
    } else {
      console.log('ℹ️ [聊天室更新] 被撤回的不是最后一条消息，无需更新');
    }

  } catch (error) {
    console.error('❌ [聊天室更新] 更新失败:', error);
    // 不抛出错误，避免影响撤回操作
  }
}

// 强制更新最后消息（如果需要）
async function forceUpdateLastMessageIfNeeded(chatRoomId, recalledMessageId) {
  try {


    // 获取聊天室信息
    const chatRoomResult = await db.collection('chatRooms').doc(chatRoomId).get();
    if (!chatRoomResult.data) {
      return;
    }

    const chatRoom = chatRoomResult.data;

    // 如果撤回的是最后一条消息，直接设置为撤回状态
    if (chatRoom.lastMessage && chatRoom.lastMessage.messageId === recalledMessageId) {
      const updatedLastMessage = {
        ...chatRoom.lastMessage,
        content: '[消息已撤回]',
        type: 'recalled'
      };

      await db.collection('chatRooms').doc(chatRoomId).update({
        data: {
          lastMessage: updatedLastMessage,
          updateTime: new Date()
        }
      });
    }

  } catch (error) {
    console.error('❌ [强制更新] 强制更新失败:', error);
  }
}

exports.main = async (event, context) => {
  const wxContext = cloud.getWXContext();
  const { action = 'send', chatRoomId, content, type = 'text', page = 1, pageSize = 20, duration } = event;

  try {
    // 验证用户
    if (!wxContext.OPENID) {
      return {
        success: false,
        error: '用户未登录'
      };
    }

    // 查询用户信息
    const userResult = await db.collection('users').where({
      openid: wxContext.OPENID
    }).get();

    if (userResult.data.length === 0) {
      return {
        success: false,
        error: '用户不存在'
      };
    }

    const user = userResult.data[0];

    // 验证必需参数
    if (!chatRoomId) {
      return {
        success: false,
        error: '缺少必需参数: chatRoomId'
      };
    }

    // 处理不同的动作
    if (action === 'send') {
      // 发送消息
      if (!content || content.trim() === '') {
        return {
          success: false,
          error: '消息内容不能为空'
        };
      }

      // 构建额外信息
      const extra = {};
      if (type === 'voice' && duration) {
        extra.duration = duration;
      }

      const result = await sendMessage(chatRoomId, content, type, user, extra);

      return {
        success: true,
        data: result,
        message: '消息发送成功'
      };
    } else if (action === 'recall') {
      // 撤回消息
      const { messageId, chatRoomId: recallChatRoomId } = event;
      if (!messageId) {
        return {
          success: false,
          error: '缺少必需参数: messageId'
        };
      }
      if (!recallChatRoomId) {
        return {
          success: false,
          error: '缺少必需参数: chatRoomId'
        };
      }

      const result = await recallMessage(messageId, user, recallChatRoomId);

      return {
        success: true,
        data: result,
        message: '消息撤回成功'
      };
      
    } else if (action === 'get') {
      // 获取消息列表
      const result = await getMessages(chatRoomId, page, pageSize, user);
      
      return {
        success: true,
        data: result
      };
      
    } else {
      return {
        success: false,
        error: '不支持的操作类型'
      };
    }

  } catch (error) {
    console.error('❌ [云函数错误]:', error);
    return {
      success: false,
      error: error.message || '服务器内部错误'
    };
  }
};
