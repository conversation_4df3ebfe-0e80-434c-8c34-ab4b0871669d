<!-- 通用返回按钮组件 -->
<view 
  class="back-button {{position}} {{size}} {{customClass}}" 
  wx:if="{{show}}"
  bindtap="handleBack"
>
  <!-- 云存储图标 -->
  <image 
    wx:if="{{backIcon && !iconLoading}}"
    class="back-icon"
    src="{{backIcon}}"
    mode="aspectFit"
  />
  
  <!-- 加载状态 -->
  <view wx:if="{{iconLoading}}" class="back-icon loading">
    <text class="loading-text">←</text>
  </view>
  
  <!-- 备用图标（当云存储图标加载失败时） -->
  <view wx:if="{{!backIcon && !iconLoading}}" class="back-icon default">
    <text class="default-icon">←</text>
  </view>
</view>
