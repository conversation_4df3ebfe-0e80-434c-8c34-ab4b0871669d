// list.js
const API = require('../../../utils/api.js');
const { buildQueryString } = require('../../../utils/urlHelper.js');
const {
  ORDER_STATUS_NAMES,
  SERVICE_TYPE_NAMES,
  COMPANION_LEVEL_NAMES
} = require('../../../utils/constants.js');

const app = getApp();

Page({
  data: {
    orderList: [],
    currentStatus: 'pending',
    currentStatusText: '待接单',
    statusTabs: [
      { label: '待接单', value: 'pending', count: 0 },
      { label: '已接单', value: 'accepted', count: 0 },
      { label: '进行中', value: 'in_progress', count: 0 }
    ],
    page: 1,
    pageSize: 10,
    loading: false,
    navLoading: false, // 导航栏加载状态
    isGrabMode: false, // 是否为抢单大厅模式
    refreshing: false,
    hasMore: true,
    floatingIcon: '', // 浮动按钮图标
    iconLoading: true, // 图标加载状态
    fromTabBar: false, // 是否从 tabBar 进入
    // 实时监听相关
    orderWatcher: null,
    isWatcherActive: false,
    // 防抖相关
    updateTimer: null,
    lastUpdateTime: 0,
    // 重连相关
    reconnectAttempts: 0,
    maxReconnectAttempts: 5,
    reconnectTimer: null,
    // 滚动位置保持
    scrollTop: 0,
    preserveScrollPosition: false
  },

  // 动态计算导航栏高度
  calculateNavigationBarHeight() {
    wx.getSystemInfo({
      success: (res) => {
        const { statusBarHeight, platform } = res;
        const isAndroid = platform === 'android';

        // 导航栏高度 = 状态栏高度 + 导航栏内容高度
        // 导航栏内容高度：iOS 50px，Android 54px（与CSS中的--height保持一致）
        const navBarContentHeight = isAndroid ? 54 : 50;
        const navBarHeight = (statusBarHeight || 44) + navBarContentHeight;

        console.log('计算的导航栏总高度:', navBarHeight, 'px', '状态栏:', statusBarHeight, 'px', '内容高度:', navBarContentHeight, 'px');

        // 动态设置CSS变量
        const style = `--nav-bar-height: ${navBarHeight}px;`;
        this.setData({
          navigationBarStyle: style
        });
      }
    });
  },

  async onLoad(options) {
    console.log('=== 订单列表页加载 ===');

    // 保存原始方法
    this.originalShowLoading = app.utils.showLoading;
    this.originalWxShowLoading = wx.showLoading;

    // 临时禁用所有loading显示
    app.utils.showLoading = () => {
      console.log('🚫 [订单列表] 阻止app.utils.showLoading调用');
    };

    wx.showLoading = () => {
      console.log('🚫 [订单列表] 阻止wx.showLoading调用');
    };

    // 立即隐藏任何可能的系统加载提示
    wx.hideLoading();
    setTimeout(() => wx.hideLoading(), 10);
    setTimeout(() => wx.hideLoading(), 50);
    setTimeout(() => wx.hideLoading(), 100);

    // 确保导航栏loading状态为false
    this.setData({ navLoading: false });

    // 动态计算导航栏高度
    this.calculateNavigationBarHeight();

    // 检查是否为抢单大厅模式
    // 如果明确指定为抢单大厅模式，或者从首页抢单大厅入口进入，则为抢单大厅模式
    // 如果明确指定为个人订单模式，则为个人订单模式
    // 否则默认为个人订单模式（我的订单）
    const isGrabMode = options.mode === 'grab' || options.from === 'grab';
    const isPersonalMode = options.mode === 'personal';

    // 获取初始状态参数
    const initialStatus = options.status || 'pending';

    if (isGrabMode) {
      // 抢单大厅模式：只显示待接单状态，隐藏其他状态选项
      this.setData({
        isGrabMode: true,
        currentStatus: 'pending',
        currentStatusText: '待接单',
        statusTabs: [
          { label: '待接单', value: 'pending', count: 0 }
        ]
      });

      // 设置页面标题
      wx.setNavigationBarTitle({
        title: '抢单大厅'
      });
    } else {
      // 个人订单模式（我的订单）或默认模式
      console.log('🔄 进入个人订单模式，isPersonalMode:', isPersonalMode, 'initialStatus:', initialStatus);

      const statusTab = this.data.statusTabs.find(tab => tab.value === initialStatus);
      this.setData({
        isGrabMode: false,
        currentStatus: initialStatus,
        currentStatusText: statusTab ? statusTab.label : '待接单',
        statusTabs: [
          { label: '待接单', value: 'pending', count: 0 },
          { label: '已接单', value: 'accepted', count: 0 },
          { label: '进行中', value: 'in_progress', count: 0 }
        ]
      });

      wx.setNavigationBarTitle({
        title: '我的订单'
      });
    }

    // 初始化浮动按钮图标
    await this.initFloatingIcon();

    // 确保用户信息已加载
    await this.ensureUserInfoLoaded();

    await this.loadOrderList(true);
    if (!isGrabMode) {
      this.loadOrderCounts();
    }

    // 在订单列表加载完成后启动实时监听（仅在抢单大厅模式下）
    if (isGrabMode) {
      // 延迟启动监听器，确保初始数据加载完成
      setTimeout(() => {
        this.startOrderWatcher();
      }, 500);
    }

    // 页面加载完成后，恢复原始的showLoading方法（延迟恢复，确保初始加载完成）
    setTimeout(() => {
      app.utils.showLoading = this.originalShowLoading;
      wx.showLoading = this.originalWxShowLoading;
      console.log('✅ [订单列表] 已恢复原始showLoading方法');
    }, 1000);
  },

  onShow() {
    console.log('=== 订单列表页显示 ===');

    // 获取app实例
    const app = getApp();

    // 临时禁用所有loading显示
    if (this.originalShowLoading) {
      app.utils.showLoading = () => {
        console.log('🚫 [订单列表显示] 阻止app.utils.showLoading调用');
      };
    }

    if (this.originalWxShowLoading) {
      wx.showLoading = () => {
        console.log('🚫 [订单列表显示] 阻止wx.showLoading调用');
      };
    }

    // 确保隐藏任何可能的系统加载提示
    wx.hideLoading();
    setTimeout(() => wx.hideLoading(), 10);
    setTimeout(() => wx.hideLoading(), 50);
    setTimeout(() => wx.hideLoading(), 100);

    // 确保导航栏loading状态为false
    this.setData({ navLoading: false });

    // 检查是否需要切换到抢单大厅模式
    if (app.globalData.enterGrabMode) {
      console.log('🎯 检测到抢单大厅模式标记，切换到抢单大厅');
      app.globalData.enterGrabMode = false; // 清除标记

      // 切换到抢单大厅模式
      this.setData({
        isGrabMode: true,
        currentStatus: 'pending',
        currentStatusText: '待接单',
        statusTabs: [
          { label: '待接单', value: 'pending', count: 0 }
        ]
      });

      // 设置页面标题
      wx.setNavigationBarTitle({
        title: '抢单大厅'
      });
    }

    // 页面显示时刷新数据
    this.loadOrderList(true);

    // 抢单大厅模式不需要加载订单统计
    if (!this.data.isGrabMode) {
      this.loadOrderCounts();
    }

    // 检查是否需要重新启动实时监听（抢单大厅模式）
    if (this.data.isGrabMode && !this.data.isWatcherActive) {
      console.log('🔄 [订单列表显示] 检测到抢单大厅模式且监听器未活跃，重新启动监听器');
      setTimeout(() => {
        this.startOrderWatcher();
      }, 500);
    }

    // 更新 tabBar 选中状态
    if (typeof this.getTabBar === 'function' && this.getTabBar()) {
      this.getTabBar().updateSelected();
    }

    // 页面显示完成后，恢复原始的showLoading方法
    setTimeout(() => {
      if (this.originalShowLoading) {
        app.utils.showLoading = this.originalShowLoading;
      }
      if (this.originalWxShowLoading) {
        wx.showLoading = this.originalWxShowLoading;
      }
      console.log('✅ [订单列表显示] 已恢复原始showLoading方法');
    }, 500);
  },

  onHide() {
    // 页面隐藏时停止实时监听
    this.stopOrderWatcher();
  },

  onUnload() {
    console.log('=== 订单列表页卸载 ===');

    // 恢复原始的showLoading方法
    if (this.originalShowLoading) {
      app.utils.showLoading = this.originalShowLoading;
    }
    if (this.originalWxShowLoading) {
      wx.showLoading = this.originalWxShowLoading;
    }

    // 页面卸载时停止实时监听
    this.stopOrderWatcher();

    // 清理防抖定时器
    if (this.data.updateTimer) {
      clearTimeout(this.data.updateTimer);
      this.setData({ updateTimer: null });
    }
  },

  // 滚动事件处理
  onScroll(e) {
    // 保存当前滚动位置
    this.setData({
      scrollTop: e.detail.scrollTop
    });
  },

  // 切换状态
  switchStatus(e) {
    const { status } = e.currentTarget.dataset;
    const statusTab = this.data.statusTabs.find(tab => tab.value === status);

    this.setData({
      currentStatus: status,
      currentStatusText: statusTab ? statusTab.label : '待接单'
    }, () => {
      this.loadOrderList(true);
    });
  },

  // 下拉刷新
  onRefresh() {
    // 强制隐藏任何可能的系统加载框
    wx.hideLoading();
    setTimeout(() => wx.hideLoading(), 10);
    setTimeout(() => wx.hideLoading(), 50);

    this.setData({ refreshing: true });

    // 记录刷新前的订单数量（用于计算新订单数量）
    const beforeRefreshCount = this.data.orderList ? this.data.orderList.length : 0;

    const promises = [this.loadOrderList(true)];

    // 抢单大厅模式不需要加载订单统计
    if (!this.data.isGrabMode) {
      promises.push(this.loadOrderCounts());
    }

    Promise.all(promises).finally(() => {
      // 强制隐藏任何可能的系统加载框
      wx.hideLoading();
      setTimeout(() => wx.hideLoading(), 10);

      this.setData({ refreshing: false });

      // 只有在抢单大厅模式下才显示新订单提示
      if (this.data.isGrabMode) {
        const afterRefreshCount = this.data.orderList ? this.data.orderList.length : 0;
        const newOrderCount = afterRefreshCount - beforeRefreshCount;

        if (newOrderCount > 0) {
          wx.showToast({
            title: `发现${newOrderCount}个新订单`,
            icon: 'none',
            duration: 1500
          });
        } else if (newOrderCount === 0 && afterRefreshCount === 0) {
          // 没有订单时的提示
          wx.showToast({
            title: '暂无新订单',
            icon: 'none',
            duration: 1000
          });
        }
      }
    });
  },

  // 加载更多
  loadMore() {
    if (!this.data.loading && this.data.hasMore) {
      this.loadOrderList(false);
    }
  },



  // 加载订单列表
  async loadOrderList(reset = false) {
    // 防止重复加载
    if (this.data.loading) {
      console.log('📋 [订单列表] 正在加载中，跳过重复请求');
      return;
    }

    const page = reset ? 1 : this.data.page + 1;
    console.log('📋 [订单列表] 开始加载，reset:', reset, 'page:', page);

    this.setData({ loading: true });

    try {
      let newList = [];
      let result;

      const { isGrabMode, currentStatus } = this.data;

      console.log('📋 [订单列表] 加载订单列表:', {
        reset,
        page,
        isGrabMode,
        currentStatus
      });

      // 使用普通API
      console.log('📋 [订单列表] 使用普通API加载');

      if (isGrabMode) {
        // 抢单大厅模式：直接进行API调用
        const grabParams = {
          page,
          pageSize: this.data.pageSize
        };



        console.log('📋 [抢单大厅] API调用参数:', grabParams);
        result = await API.getGrabOrderList(grabParams);
      } else {
        // 普通订单列表模式
        const params = {
          page,
          pageSize: this.data.pageSize,
          status: currentStatus
        };

        // 修复订单可见性问题：所有状态都查询用户相关的所有订单
        // 无论用户是发布者还是接单者，都应该能看到相关订单
        if (currentStatus === 'pending') {
          // 待接单：只查询用户作为发布者的订单（因为接单者还没有接单）
          params.role = 'customer';
        } else {
          // 已接单、进行中、已完成等状态：查询所有相关订单（作为发布者或接单者）
          // 这样发布者可以看到自己发布但被别人接单的订单
          // 接单者也可以看到自己接的订单
          params.role = 'all';
        }

        console.log('📋 [订单列表] API调用参数:', params);
        result = await API.getOrderList(params);
        console.log('📋 [订单列表] API调用结果:', result);
      }

      if (result && result.success) {
        // 前端额外筛选，确保状态匹配
        let filteredList = result.data.list;
        if (currentStatus && currentStatus !== 'all') {
          filteredList = result.data.list.filter(order => order.status === currentStatus);
        }

        newList = filteredList.map(order => this.formatOrderData(order));
      }

      // 合并数据并去重
      const combinedList = reset ? newList : [...this.data.orderList, ...newList];
      // 简单去重逻辑：根据订单ID去重
      const seen = new Set();
      const uniqueOrders = combinedList.filter(order => {
        if (seen.has(order._id)) {
          return false;
        }
        seen.add(order._id);
        return true;
      });

      this.setData({
        orderList: uniqueOrders,
        page,
        hasMore: newList.length === this.data.pageSize
      });

      console.log('✅ [订单列表] 加载完成，订单数量:', uniqueOrders.length);

    } catch (error) {
      console.error('❌ [订单列表] 加载失败:', error);
      wx.showToast({
        title: '加载失败',
        icon: 'error'
      });
    } finally {
      this.setData({ loading: false });
      wx.hideLoading();
    }
  },

  // 加载订单数量统计
  async loadOrderCounts() {
    try {
      let counts = {};

      // 检查是否为演示模式
      const userInfo = app.globalData.userInfo;
      if (userInfo && userInfo.isDemo) {
        // 演示模式：显示空状态
        counts = {
          pending: 0,
          accepted: 0,
          in_progress: 0,
          completed: 0
        };
      } else {
        // 正常模式：调用API
        try {
          const result = await API.getOrderCounts();
          if (result.success) {
            counts = result.data;
          }
        } catch (apiError) {
          console.log('API调用失败，显示空状态:', apiError);
          counts = {
            pending: 0,
            accepted: 0,
            in_progress: 0,
            completed: 0
          };
        }
      }

      const statusTabs = this.data.statusTabs.map(tab => ({
        ...tab,
        count: counts[tab.value] || 0
      }));
      this.setData({ statusTabs });
    } catch (error) {
      console.error('加载订单统计失败:', error);
    }
  },








  // 清理文本内容，去除多余空格和换行符
  cleanTextContent(text) {
    if (!text) return '';
    // 将换行符替换为空格，然后去除多余的空格
    return text.replace(/\n/g, ' ').replace(/\s+/g, ' ').trim();
  },

  // 格式化订单数据
  formatOrderData(order) {

    // 尝试从本地存储获取修改后的数据
    let modifiedData = {};
    try {
      const storedData = wx.getStorageSync(`create_order_${order._id}`);
      if (storedData) {
        modifiedData = storedData;
      }
    } catch (error) {
      console.warn('读取本地存储订单数据失败:', error);
    }

    // 合并修改后的数据
    const finalOrder = {
      ...order,
      title: modifiedData.title || order.title,
      content: modifiedData.content || order.content,
      reward: modifiedData.reward || order.reward || order.totalAmount,
      // 平台类型
      platformType: modifiedData.platformType || order.platformType,
      // 服务类型和时间信息
      serviceType: modifiedData.serviceType || order.serviceType,
      duration: modifiedData.duration || order.duration,
      rounds: modifiedData.rounds || order.rounds,
      tags: modifiedData.tags && modifiedData.tags.length > 0 ? modifiedData.tags : order.tags,
      orderType: modifiedData.orderType || order.orderType,
      scheduledDate: modifiedData.scheduledDate || order.scheduledDate,
      scheduledTime: modifiedData.scheduledTime || order.scheduledTime
    };

    console.log('🔍 [格式化订单] 原始订单platformType:', order.platformType);
    console.log('🔍 [格式化订单] 本地存储platformType:', modifiedData.platformType);
    console.log('🔍 [格式化订单] 最终订单platformType:', finalOrder.platformType);

    const result = {
      ...finalOrder,
      // 清理文本内容
      content: this.cleanTextContent(finalOrder.content),
      title: this.cleanTextContent(finalOrder.title),
      statusText: ORDER_STATUS_NAMES[finalOrder.status] || finalOrder.status,
      serviceTypeText: SERVICE_TYPE_NAMES[finalOrder.serviceType] || finalOrder.serviceType,
      createTimeText: this.formatTime(finalOrder.createTime),
      companionInfo: finalOrder.companionInfo ? {
        ...finalOrder.companionInfo,
        levelText: COMPANION_LEVEL_NAMES[finalOrder.companionInfo.level] || finalOrder.companionInfo.level
      } : null,
      // 添加游戏信息以保持与首页一致，优先使用新的title字段
      gameInfo: finalOrder.gameInfo || {
        gameName: this.cleanTextContent(finalOrder.title) || SERVICE_TYPE_NAMES[finalOrder.serviceType] || '高阶技术指导',
        gameMode: finalOrder.gameMode || null // 不设置默认游戏模式
      },
      // 确保价格信息格式一致，支持新的reward字段
      pricing: finalOrder.pricing || {
        totalAmount: finalOrder.reward || finalOrder.totalAmount || 0
      },
      // 确保需求信息格式一致
      requirements: finalOrder.requirements || {
        duration: finalOrder.duration || 2
      },
      // 兼容新旧数据结构
      totalAmount: finalOrder.reward || finalOrder.pricing?.totalAmount || finalOrder.totalAmount || 0,
      // 确保标签显示正确
      displayTags: finalOrder.tags || [],
      // 判断是否为订单发布者
      isOwner: this.isOrderOwner(finalOrder)
    };

    return result;
  },

  // 格式化时间
  formatTime(dateStr) {
    const date = new Date(dateStr);
    const now = new Date();
    const diff = now - date;

    if (diff < 60000) { // 1分钟内
      return '刚刚';
    } else if (diff < 3600000) { // 1小时内
      return `${Math.floor(diff / 60000)}分钟前`;
    } else if (diff < 86400000) { // 1天内
      return `${Math.floor(diff / 3600000)}小时前`;
    } else {
      const month = String(date.getMonth() + 1).padStart(2, '0');
      const day = String(date.getDate()).padStart(2, '0');
      return `${month}-${day}`;
    }
  },

  // 导航到详情页
  navigateToDetail(e) {
    const orderId = e.detail.orderId || e.currentTarget.dataset.id;
    wx.navigateTo({
      url: `/order-package/pages/detail/detail?id=${orderId}`
    });
  },

  // 查看订单详情（组件事件）
  viewOrderDetail(e) {
    const orderId = e.detail.orderId;
    const orderData = e.detail.orderData;
    console.log('订单大厅 - 查看订单详情:', { orderId, orderData, event: e });


    if (!orderId) {
      console.error('订单ID为空:', e);
      wx.showToast({
        title: '订单ID错误',
        icon: 'error'
      });
      return;
    }

    wx.navigateTo({
      url: `/order-package/pages/detail/detail?id=${orderId}`
    });
  },

  // 通用取消原因选择函数
  showCancelReasonSelector(callback) {
    wx.showActionSheet({
      itemList: ['临时有事', '价格问题', '服务质量问题', '沟通问题', '其他问题'],
      success: (res) => {
        const reasons = ['临时有事', '价格问题', '服务质量问题', '沟通问题', '其他问题'];
        const selectedReason = reasons[res.tapIndex];

        if (selectedReason === '其他问题') {
          // 显示输入框让用户输入具体原因
          wx.showModal({
            title: '请输入取消原因',
            content: '请详细说明取消原因：',
            editable: true,
            placeholderText: '请输入具体的取消原因...',
            success: (inputRes) => {
              if (inputRes.confirm) {
                const customReason = inputRes.content.trim();
                if (customReason) {
                  callback(customReason);
                } else {
                  wx.showToast({
                    title: '请输入取消原因',
                    icon: 'none'
                  });
                }
              }
            }
          });
        } else {
          callback(selectedReason);
        }
      }
    });
  },

  // 取消订单
  cancelOrder(e) {
    const orderId = e.detail.orderId || e.currentTarget.dataset.id;

    this.showCancelReasonSelector(async (reason) => {
      wx.showModal({
        title: '确认取消',
        content: `确定要取消这个订单吗？\n取消原因：${reason}`,
        success: async (modalRes) => {
          if (modalRes.confirm) {
            // 直接调用API，API层会处理演示模式
            try {
              wx.showLoading({
                title: '取消中...'
              });

              // 确定用户角色
              const userInfo = app.globalData.userInfo;
              const order = this.data.orderList.find(item => item._id === orderId);
              let userRole = 'customer'; // 默认为发单者

              if (order && userInfo) {
                // 如果当前用户是接单者
                if (order.accepterId === userInfo._id) {
                  userRole = 'accepter';
                }
                // 如果当前用户是发单者（通过openid比较）
                else if (order.customerInfo && order.customerInfo.openid === userInfo.openid) {
                  userRole = 'customer';
                }
              }

              console.log('🔍 [订单列表-取消订单] 用户角色判断:', {
                orderId,
                currentUserId: userInfo?._id,
                currentUserOpenid: userInfo?.openid,
                orderAccepterId: order?.accepterId,
                orderCustomerOpenid: order?.customerInfo?.openid,
                determinedRole: userRole
              });

              const result = await API.cancelOrder(orderId, reason, userRole);
              wx.hideLoading();

              if (result.success) {
                app.utils.showSuccess(result.message || '订单已取消');

                // 如果是演示模式，直接从列表中移除订单
                if (result.message && result.message.includes('演示模式')) {
                  const orderList = this.data.orderList.filter(item => item._id !== orderId);
                  this.setData({ orderList });
                } else {
                  // 正常模式，重新加载列表
                  this.loadOrderList(true);
                  if (!this.data.isGrabMode) {
                    this.loadOrderCounts();
                  }
                }
              } else {
                app.utils.showError(result.error || '取消失败');
              }
            } catch (error) {
              wx.hideLoading();
              console.error('取消订单失败:', error);
              app.utils.showError('取消失败，请重试');
            }
          }
        }
      });
    });
  },

  // 联系接单者（组件事件）
  contactAccepter(e) {
    const orderId = e.detail.orderId;
    const order = this.data.orderList.find(item => item._id === orderId);

    if (order && order.accepterInfo) {
      wx.navigateTo({
        url: `/chat-package/pages/room/room?orderId=${orderId}&accepterId=${order.accepterInfo._id}`
      });
    } else {
      wx.showToast({
        title: '接单者信息不存在',
        icon: 'error'
      });
    }
  },

  // 进入聊天（组件事件）
  enterChat(e) {
    const orderId = e.detail.orderId;
    const order = this.data.orderList.find(item => item._id === orderId);

    if (order && order.chatRoomId) {
      wx.navigateTo({
        url: `/chat-package/pages/room/room?roomId=${order.chatRoomId}&orderId=${orderId}`
      });
    } else {
      // 如果没有聊天室，尝试创建或进入聊天
      wx.navigateTo({
        url: `/chat-package/pages/room/room?orderId=${orderId}`
      });
    }
  },

  // 进入聊天室
  enterChatRoom(e) {
    const { id } = e.currentTarget.dataset;
    const order = this.data.orderList.find(item => item._id === id);

    if (order && order.chatRoomId) {
      wx.navigateTo({
        url: `/chat-package/pages/room/room?roomId=${order.chatRoomId}&orderId=${id}`
      });
    }
  },

  // 完成订单
  completeOrder(e) {
    const { id } = e.currentTarget.dataset;

    wx.showModal({
      title: '确认完成',
      content: '确认订单已完成吗？',
      success: async (res) => {
        if (res.confirm) {
          try {
            const result = await API.completeOrder(id);
            if (result.success) {
              app.utils.showSuccess('订单已完成');
              this.loadOrderList(true);
              this.loadOrderCounts();
            }
          } catch (error) {
            app.utils.showError('操作失败');
          }
        }
      }
    });
  },

  // 评价订单（组件事件）
  evaluateOrder(e) {
    const orderId = e.detail.orderId || e.currentTarget.dataset.id;
    wx.navigateTo({
      url: `/order-package/pages/evaluation/evaluation?orderId=${orderId}`
    });
  },

  // 重新下单
  reorder(e) {
    const { id } = e.currentTarget.dataset;
    const order = this.data.orderList.find(item => item._id === id);

    if (order) {
      let url = '/order-package/pages/create/create';
      if (order.companionInfo) {
        url += `?companionId=${order.companionInfo._id}`;
      }
      wx.navigateTo({ url });
    }
  },

  // 导航到创建订单
  navigateToCreate() {
    wx.navigateTo({
      url: '/order-package/pages/create/create'
    });
  },



  // 抢单
  grabOrder(e) {
    const orderId = e.detail.orderId || e.currentTarget.dataset.id;
    const order = this.data.orderList.find(item => item._id === orderId);

    if (!order) {
      wx.showToast({
        title: '订单不存在',
        icon: 'error'
      });
      return;
    }

    // 构建服务时间显示文本
    let serviceText = '';
    if (order.serviceType === 'rounds' || (order.rounds && !order.serviceType)) {
      serviceText = `局数：${order.rounds || 5}局`;
    } else {
      serviceText = `时长：${order.duration || 2}小时`;
    }

    wx.showModal({
      title: '确认抢单',
      content: `确定要抢这个订单吗？\n金额：¥${order.totalAmount || order.reward || 0}\n${serviceText}`,
      confirmText: '确认抢单',
      cancelText: '取消',
      success: (res) => {
        if (res.confirm) {
          this.confirmGrabOrder(orderId);
        }
      }
    });
  },

  // 确认抢单
  async confirmGrabOrder(orderId) {
    // 防重复点击检查
    if (this.data.isGrabbing) {
      console.log('🚫 [防重复] 正在抢单中，忽略重复点击');
      return;
    }

    try {
      // 设置抢单状态
      this.setData({ isGrabbing: true });

      wx.showLoading({
        title: '抢单中...',
        mask: true
      });

      // 调用抢单API
      const result = await API.grabOrder(orderId);

      // 处理抢单结果
      if (result.success) {
        wx.hideLoading();
        console.log('✅ 抢单成功');

        // 重置抢单状态
        this.setData({ isGrabbing: false });

        wx.showModal({
          title: '抢单成功！',
          content: '恭喜您成功抢到订单！是否查看我的订单？',
          confirmText: '查看订单',
          cancelText: '继续抢单',
          success: (res) => {
            if (res.confirm) {
              // 跳转到订单详情页面
              console.log('🔗 跳转到订单详情页面，订单ID:', orderId);
              wx.navigateTo({
                url: `/order-package/pages/detail/detail?id=${orderId}`
              });
            } else {
              // 刷新订单列表继续抢单
              this.loadOrderList(true);

              // 如果不是抢单模式，也刷新订单统计
              if (!this.data.isGrabMode) {
                this.loadOrderCounts();
              }
            }
          }
        });

        // 无论用户选择什么，都要刷新列表（移除已抢的订单）
        setTimeout(() => {
          this.loadOrderList(true);
          if (!this.data.isGrabMode) {
            this.loadOrderCounts();
          }
        }, 500);
      } else {
        // 特殊情况：如果是ORDER_ALREADY_TAKEN，快速检查是否为当前用户抢到
        if (result.errorCode === 'ORDER_ALREADY_TAKEN') {
          console.log('🔍 [智能检查] ORDER_ALREADY_TAKEN，快速验证是否为当前用户抢到');

          try {
            // 使用更快的直接数据库查询
            const checkResult = await wx.cloud.database().collection('orders')
              .doc(orderId)
              .field({ accepterId: true, status: true })
              .get();
            if (checkResult.data &&
                checkResult.data.accepterId === app.globalData.userInfo._id) {
              console.log('✅ [智能检查] 确认是当前用户抢到，显示成功');

              wx.hideLoading();
              this.setData({ isGrabbing: false });

              wx.showModal({
                title: '抢单成功！',
                content: '恭喜您成功抢到订单！是否查看我的订单？',
                confirmText: '查看订单',
                cancelText: '继续抢单',
                success: (res) => {
                  if (res.confirm) {
                    wx.navigateTo({
                      url: `/order-package/pages/detail/detail?id=${orderId}`
                    });
                  } else {
                    this.loadOrderList(true);
                    if (!this.data.isGrabMode) {
                      this.loadOrderCounts();
                    }
                  }
                }
              });
              return;
            }
          } catch (checkError) {
            console.log('🔍 [智能检查] 检查失败，继续显示错误');
          }
        }

        // 立即隐藏loading并处理错误
        wx.hideLoading();

        console.log('❌ [抢单失败] 确认失败，显示错误信息');

        // 确实是抢单失败，处理错误信息
        let errorMessage = result.error || '抢单失败';
        let shouldRefreshList = false;

        // 优先使用错误代码进行判断
        if (result.errorCode) {
          switch (result.errorCode) {
            case 'ORDER_ALREADY_TAKEN':
            case 'CONCURRENT_CONFLICT':
            case 'MAX_RETRIES_EXCEEDED':
            case 'CONCURRENT_UPDATE_FAILED':
            case 'VERIFICATION_FAILED':
              errorMessage = '此订单已被其他用户抢走';
              shouldRefreshList = true;
              break;
            case 'ORDER_CANCELLED':
              errorMessage = '此订单已被取消';
              shouldRefreshList = true;
              break;
            case 'ORDER_COMPLETED':
              errorMessage = '此订单已完成';
              shouldRefreshList = true;
              break;
            case 'ORDER_NOT_FOUND':
              errorMessage = '订单不存在';
              shouldRefreshList = true;
              break;
            case 'INVALID_STATUS':
              errorMessage = '订单状态已变更，无法抢单';
              shouldRefreshList = true;
              break;
            case 'OWN_ORDER':
              errorMessage = '不能抢自己发布的订单';
              break;
            case 'USER_NOT_FOUND':
              errorMessage = '用户信息异常，请重新登录';
              break;
            default:
              // 使用原始错误消息
              break;
          }
        } else {
          // 兼容旧版本：根据错误信息进行判断
          if (errorMessage.includes('不存在') ||
              errorMessage.includes('已被') ||
              errorMessage.includes('已取消') ||
              errorMessage.includes('订单状态不允许接单') ||
              errorMessage.includes('状态不允许') ||
              errorMessage.includes('订单不可用')) {
            errorMessage = '此订单已被抢或已取消';
            shouldRefreshList = true;
          }
        }

        console.log('🚫 [抢单失败] 错误处理:', {
          originalError: result.error,
          errorCode: result.errorCode,
          finalMessage: errorMessage,
          shouldRefresh: shouldRefreshList
        });

        wx.showToast({
          title: errorMessage,
          icon: 'error',
          duration: 2500
        });

        // 如果是订单状态相关的错误，刷新列表移除无效订单
        if (shouldRefreshList) {
          setTimeout(() => {
            console.log('🔄 [自动刷新] 由于订单状态错误，刷新订单列表');
            this.loadOrderList(true);
          }, 1000);
        }
      }

    } catch (error) {
      wx.hideLoading();
      console.error('抢单失败:', error);
      wx.showToast({
        title: '抢单失败，请重试',
        icon: 'error'
      });
    } finally {
      // 重置抢单状态
      this.setData({ isGrabbing: false });
    }
  },

  // 初始化浮动按钮图标
  async initFloatingIcon() {
    try {
      console.log('🔍 [订单大厅] 开始初始化浮动按钮图标...');

      // 使用临时URL方式获取云存储图标
      const cloudFileId = 'cloud://cloud1-9gsj7t48183e5a9f.636c-cloud1-9gsj7t48183e5a9f-1366958750/images/icons/your-icon.png';

      const result = await wx.cloud.getTempFileURL({
        fileList: [cloudFileId]
      });

      if (result.fileList && result.fileList.length > 0 && result.fileList[0].status === 0) {
        const floatingIcon = result.fileList[0].tempFileURL;
        this.setData({
          floatingIcon,
          iconLoading: false
        });
        console.log('✅ [订单大厅] 浮动按钮图标设置成功:', floatingIcon);
      } else {
        throw new Error('云存储图标获取失败');
      }

    } catch (error) {
      console.error('❌ [订单大厅] 初始化浮动按钮图标失败:', error);
      // 使用文字图标作为备用
      this.setData({
        floatingIcon: '',
        iconLoading: false
      });
      console.log('🔄 [订单大厅] 使用默认样式');
    }
  },

  // 确保用户信息已加载
  async ensureUserInfoLoaded() {
    const maxRetries = 10;
    let retries = 0;

    while (retries < maxRetries) {
      const userInfo = app.globalData.userInfo;
      if (userInfo && userInfo.openid) {
        console.log('✅ [用户信息] 用户信息已准备就绪:', userInfo.openid);
        return;
      }

      console.log(`⏳ [用户信息] 等待用户信息加载... (${retries + 1}/${maxRetries})`);
      await new Promise(resolve => setTimeout(resolve, 100));
      retries++;
    }

    console.warn('⚠️ [用户信息] 用户信息加载超时，继续执行');
  },

  // 判断是否为订单发布者
  isOrderOwner(order) {
    const userInfo = app.globalData.userInfo;

    console.log('isOrderOwner - 调试信息:', {
      currentUserOpenid: userInfo?.openid,
      orderCustomerOpenid: order.customerInfo?.openid,
      orderId: order._id,
      hasUserInfo: !!userInfo,
      hasOrderCustomerInfo: !!order.customerInfo
    });

    // 如果用户信息或订单客户信息不完整，返回false（显示抢单按钮）
    if (!userInfo || !userInfo.openid || !order.customerInfo || !order.customerInfo.openid) {
      console.log('缺少必要的用户信息，默认不是订单发布者');
      return false;
    }

    const isOwner = userInfo.openid === order.customerInfo.openid;
    console.log('是否为订单发布者:', isOwner);
    return isOwner;
  },

  // 修改订单
  editOrder(e) {
    const orderId = e.detail.orderId || e.currentTarget.dataset.id;
    const order = this.data.orderList.find(item => item._id === orderId);

    if (order) {
      // 手动构建URL参数字符串（微信小程序不支持URLSearchParams）
      const params = {
        mode: 'edit',
        orderId: order._id,
        title: order.title || order.gameInfo?.gameName || '',
        content: order.content || '',
        reward: order.reward || order.totalAmount || '',
        platformType: order.platformType || 'pc',
        serviceType: order.serviceType || 'duration',
        duration: order.duration || '',
        rounds: order.rounds || '',
        orderType: order.orderType || 'immediate',
        scheduledDate: order.scheduledDate || '',
        scheduledTime: order.scheduledTime || ''
      };

      console.log('🔍 [订单列表-编辑订单] 传递的参数:', params);

      // 构建查询字符串
      const queryString = buildQueryString(params);

      // 跳转到订单编辑页面
      wx.navigateTo({
        url: `/order-package/pages/create/create?${queryString}`
      });
    }
  },







  // 启动订单实时监听（仅在抢单大厅模式下）
  startOrderWatcher() {
    if (this.data.isWatcherActive || !this.data.isGrabMode) {
      console.log('🚀 [实时监听] 跳过启动监听器 - isWatcherActive:', this.data.isWatcherActive, 'isGrabMode:', this.data.isGrabMode);
      return;
    }

    console.log('🔄 [实时监听] 启动抢单大厅订单监听器');
    console.log('🚀 [实时监听] 当前状态检查:', {
      isWatcherActive: this.data.isWatcherActive,
      isGrabMode: this.data.isGrabMode,
      orderWatcher: this.data.orderWatcher ? 'exists' : 'null'
    });

    try {
      const db = wx.cloud.database();

      // 监听待接单状态的订单变更（抢单大厅只显示pending状态的订单）
      const watcher = db.collection('orders')
        .where({
          status: 'pending'  // 只监听待接单的订单
        })
        .orderBy('createTime', 'desc')
        .limit(100) // 增加监听范围，确保能捕获所有相关变更
        .watch({
          onChange: (snapshot) => {
            // 确保页面仍然存在且监听器仍然活跃
            if (this.data && this.data.isWatcherActive && this.data.isGrabMode) {
              console.log('📡 [实时监听] 收到变更事件:', snapshot.type, 'docChanges:', snapshot.docChanges?.length);
              console.log('📡 [实时监听] 当前页面状态:', {
                isWatcherActive: this.data.isWatcherActive,
                isGrabMode: this.data.isGrabMode,
                hasData: !!this.data
              });
              this.handleOrderChange(snapshot);
            } else {
              console.log('📡 [实时监听] 忽略变更事件 - 页面状态不符合条件:', {
                hasData: !!this.data,
                isWatcherActive: this.data?.isWatcherActive,
                isGrabMode: this.data?.isGrabMode
              });
            }
          },
          onError: (error) => {
            console.error('❌ [实时监听] 订单监听器错误:', error);

            // 更新状态
            if (this.data) {
              this.setData({
                isWatcherActive: false,
                orderWatcher: null
              });
            }

            // 监听失败时，使用指数退避算法重新启动
            this.scheduleReconnect();
          }
        });

      // 保存监听器引用并更新状态
      this.setData({
        orderWatcher: watcher,
        isWatcherActive: true,
        reconnectAttempts: 0 // 成功启动时重置重连计数器
      });

      console.log('✅ [实时监听] 订单监听器启动成功');
      console.log('✅ [实时监听] 最终状态:', {
        isWatcherActive: this.data.isWatcherActive,
        isGrabMode: this.data.isGrabMode,
        hasWatcher: !!this.data.orderWatcher
      });
    } catch (error) {
      console.error('❌ [实时监听] 启动订单监听器失败:', error);

      // 启动失败时重置状态
      this.setData({
        isWatcherActive: false,
        orderWatcher: null
      });
    }
  },

  // 停止订单实时监听
  stopOrderWatcher() {
    if (!this.data.isWatcherActive || !this.data.orderWatcher) {
      return;
    }

    console.log('🛑 [实时监听] 停止订单监听器');

    try {
      // 关闭监听器
      this.data.orderWatcher.close();
    } catch (error) {
      console.error('❌ [实时监听] 关闭监听器失败:', error);
    }

    // 清除重连定时器
    if (this.data.reconnectTimer) {
      clearTimeout(this.data.reconnectTimer);
    }

    // 清除状态
    this.setData({
      orderWatcher: null,
      isWatcherActive: false,
      reconnectAttempts: 0,
      reconnectTimer: null
    });
  },

  // 调度重连
  scheduleReconnect() {
    if (this.data.reconnectAttempts >= this.data.maxReconnectAttempts) {
      console.error('❌ [实时监听] 重连次数已达上限，停止重连');
      wx.showToast({
        title: '网络连接异常，请手动刷新',
        icon: 'none',
        duration: 3000
      });
      return;
    }

    // 清除之前的重连定时器
    if (this.data.reconnectTimer) {
      clearTimeout(this.data.reconnectTimer);
    }

    // 使用指数退避算法计算延迟时间
    const delay = Math.min(1000 * Math.pow(2, this.data.reconnectAttempts), 30000); // 最大30秒

    console.log(`🔄 [实时监听] 计划在 ${delay}ms 后进行第 ${this.data.reconnectAttempts + 1} 次重连`);

    const timer = setTimeout(() => {
      if (this.data && !this.data.isWatcherActive && this.data.isGrabMode) {
        this.setData({
          reconnectAttempts: this.data.reconnectAttempts + 1
        });
        console.log(`🔄 [实时监听] 开始第 ${this.data.reconnectAttempts} 次重连尝试`);
        this.startOrderWatcher();
      }
    }, delay);

    this.setData({
      reconnectTimer: timer
    });
  },

  // 处理订单变更事件
  handleOrderChange(snapshot) {
    console.log('📡 [实时监听] 收到订单变更事件:', snapshot.type);

    // 使用 docChanges 精确处理变更
    if (snapshot.docChanges && snapshot.docChanges.length > 0) {
      snapshot.docChanges.forEach(change => {
        console.log('📡 [实时监听] 处理变更:', change.changeType, change.doc._id, '状态:', change.doc.status);

        switch (change.changeType) {
          case 'add':
            // 新增订单 - 只有pending状态的订单才显示在抢单大厅
            if (change.doc.status === 'pending') {
              this.handleOrderAdd(change.doc);
            }
            break;

          case 'update':
            // 订单更新 - 检查状态变更
            this.handleOrderUpdate(change.doc);
            break;

          case 'remove':
            // 订单删除或状态变更导致的移除 - 从列表中移除
            this.handleOrderRemove(change.doc);
            break;

          case undefined:
          case null:
            // 处理 changeType 为 undefined 的情况（微信小程序可能的API差异）
            console.log('📡 [实时监听] changeType 为空，根据订单状态判断操作');
            if (change.doc.status === 'pending') {
              // 检查订单是否已在列表中
              const currentList = this.data.orderList || [];
              const existingOrder = currentList.find(order => order._id === change.doc._id);

              if (!existingOrder) {
                console.log('📡 [实时监听] 新订单，添加到列表');
                this.handleOrderAdd(change.doc);
              } else {
                console.log('📡 [实时监听] 订单已存在，更新信息');
                this.handleOrderUpdate(change.doc);
              }
            } else {
              // 非pending状态，从列表中移除
              console.log('📡 [实时监听] 非pending状态，从列表移除');
              this.handleOrderRemove(change.doc);
            }
            break;

          default:
            console.log('📡 [实时监听] 未知变更类型:', change.changeType);
            break;
        }
      });
    } else {
      // 兼容旧的处理方式
      switch (snapshot.type) {
        case 'init':
          console.log('📡 [实时监听] 初始化数据');
          break;
        case 'update':
          console.log('📡 [实时监听] 订单更新事件');
          this.handleOrderUpdateLegacy(snapshot);
          break;
        case 'add':
          console.log('📡 [实时监听] 新增订单事件');
          this.handleOrderAddLegacy(snapshot);
          break;
        case 'remove':
          console.log('📡 [实时监听] 订单删除事件');
          this.handleOrderRemoveLegacy(snapshot);
          break;
        default:
          console.log('📡 [实时监听] 未知变更类型:', snapshot.type);
          this.loadOrderList(true);
          break;
      }
    }
  },

  // 处理订单更新事件（新版本 - 使用单个订单）
  handleOrderUpdate(updatedOrder) {
    console.log('📡 [实时监听] 订单更新:', updatedOrder._id, updatedOrder.status);

    // 获取当前订单列表
    const currentList = this.data.orderList || [];
    const existingOrder = currentList.find(order => order._id === updatedOrder._id);

    if (updatedOrder.status === 'pending') {
      // 如果订单状态是pending，检查是否需要添加到列表
      if (!existingOrder) {
        console.log('📡 [实时监听] 订单状态变更为pending，添加到抢单大厅:', updatedOrder._id);
        this.handleOrderAdd(updatedOrder);
      } else {
        // 如果已存在，更新订单信息
        console.log('📡 [实时监听] 更新现有订单信息:', updatedOrder._id);
        this.updateOrderInList(updatedOrder);
      }
    } else {
      // 如果订单状态不是pending，需要从列表中移除
      if (existingOrder) {
        console.log('📡 [实时监听] 订单状态变更，从抢单大厅移除:', updatedOrder._id, updatedOrder.status);

        // 直接从列表中移除订单
        this.removeOrderFromList(updatedOrder._id);

        // 显示提示
        wx.showToast({
          title: '订单状态已更新',
          icon: 'none',
          duration: 1500
        });
      }
    }
  },

  // 更新列表中的订单信息
  updateOrderInList(updatedOrder) {
    try {
      console.log('📡 [实时监听] 开始更新订单信息:', updatedOrder._id);

      // 获取当前订单列表
      const currentList = this.data.orderList || [];

      // 更新对应的订单
      const updatedList = currentList.map(order => {
        if (order._id === updatedOrder._id) {
          // 保持原有的客户信息，避免丢失 openid 等关键信息
          const preservedCustomerInfo = order.customerInfo;

          // 格式化更新后的订单数据，但保持客户信息不变
          const formattedOrder = this.formatNewOrderDataInstantly(updatedOrder);

          // 如果原订单有完整的客户信息，则保持不变
          if (preservedCustomerInfo && preservedCustomerInfo.openid) {
            formattedOrder.customerInfo = preservedCustomerInfo;
            // 重新计算 isOwner，使用保持的客户信息
            formattedOrder.isOwner = this.isOrderOwner(formattedOrder);
            console.log('📡 [实时监听] 保持原有客户信息，isOwner:', formattedOrder.isOwner);
          }

          return formattedOrder;
        }
        return order;
      });

      // 更新列表
      this.setData({
        orderList: updatedList
      });

      console.log('✅ [实时监听] 订单信息已更新:', updatedOrder._id);

    } catch (error) {
      console.error('❌ [实时监听] 更新订单信息失败:', error);
    }
  },

  // 处理订单删除事件（新版本 - 使用单个订单）
  handleOrderRemove(removedOrder) {
    console.log('📡 [实时监听] 订单删除或移除:', removedOrder._id, '状态:', removedOrder.status);

    // 检查订单是否在当前列表中
    const currentList = this.data.orderList || [];
    const existingOrder = currentList.find(order => order._id === removedOrder._id);

    if (existingOrder) {
      console.log('📡 [实时监听] 从抢单大厅静默移除订单:', removedOrder._id);

      // 静默从列表中移除订单，不显示任何提示
      this.removeOrderFromList(removedOrder._id);

      // 注意：不再显示"订单已被抢"或"订单已删除"的提示
      // 只有在用户主动抢单时遇到冲突才会显示相关提示
      console.log('✅ [实时监听] 订单已静默移除，无提示显示');
    } else {
      console.log('📡 [实时监听] 订单不在当前列表中，无需移除:', removedOrder._id);
    }
  },

  // 从列表中移除单个订单
  removeOrderFromList(orderId) {
    try {
      console.log('📡 [实时监听] 开始移除订单:', orderId);

      // 获取当前订单列表
      const currentList = this.data.orderList || [];

      // 过滤掉需要移除的订单
      const filteredList = currentList.filter(order => order._id !== orderId);

      // 更新列表
      this.setData({
        orderList: filteredList
      });

      console.log('✅ [实时监听] 订单已移除，当前订单数:', filteredList.length);

    } catch (error) {
      console.error('❌ [实时监听] 移除订单失败:', error);
    }
  },

  // 直接从列表中移除订单（避免完整重新加载）
  removeOrdersFromList(orderIdsToRemove) {
    try {
      console.log('📡 [实时监听] 开始移除订单，避免完整重新加载:', orderIdsToRemove);

      // 获取当前订单列表
      const currentList = this.data.orderList || [];

      // 过滤掉需要移除的订单
      const filteredList = currentList.filter(order => !orderIdsToRemove.includes(order._id));

      // 更新列表
      this.setData({
        orderList: filteredList
      });

      console.log('✅ [实时监听] 订单已移除，当前订单数:', filteredList.length);

    } catch (error) {
      console.error('❌ [实时监听] 移除订单失败，回退到完整重新加载:', error);
      // 如果直接移除失败，回退到完整重新加载
      this.loadOrderList(true);
    }
  },

  // 处理新增订单事件（新版本 - 使用单个订单）
  handleOrderAdd(newOrder) {
    try {
      // 获取当前订单列表
      const currentList = this.data.orderList || [];

      // 检查订单是否已存在（避免重复添加）
      const existingOrder = currentList.find(order => order._id === newOrder._id);
      if (existingOrder) {
        console.log('📡 [实时监听] 订单已存在，跳过添加:', newOrder._id);
        return;
      }

      // 直接格式化新订单（使用监听器返回的完整数据，无延迟）
      const formattedOrder = this.formatNewOrderDataInstantly(newOrder);

      // 静默插入到列表顶部
      const updatedList = [formattedOrder, ...currentList];

      // 立即更新列表，无任何加载状态
      this.setData({
        orderList: updatedList
      });

      console.log('✅ [实时监听] 新订单已静默添加到列表顶部:', newOrder._id);

      // 注意：不再主动显示新订单提示
      // 只有在用户手动下拉刷新时才会显示新订单数量提示

    } catch (error) {
      console.error('❌ [实时监听] 添加新订单失败:', error);
    }
  },

  // 简化的新订单格式化方法（无延迟）
  formatNewOrderDataInstantly(order) {
    console.log('🔍 [订单列表实时格式化] 原始订单数据:', order);
    console.log('🔍 [订单列表实时格式化] 原始platformType字段:', order.platformType);

    // 如果实时监听数据中缺少platformType，尝试从本地存储获取
    let platformType = order.platformType;
    if (!platformType) {
      try {
        const storedData = wx.getStorageSync(`create_order_${order._id}`);
        if (storedData && storedData.platformType) {
          platformType = storedData.platformType;
          console.log('🔍 [实时格式化] 从本地存储获取platformType:', platformType);
        }
      } catch (error) {
        console.warn('读取本地存储platformType失败:', error);
      }
    }

    const result = {
      ...order,
      // 确保platformType字段存在，优先使用原始数据
      platformType: order.platformType || platformType || 'pc',
      // 基本显示信息
      statusText: ORDER_STATUS_NAMES[order.status] || order.status,
      serviceTypeText: SERVICE_TYPE_NAMES[order.serviceType] || order.serviceType,
      createTimeText: this.formatTime(order.createTime),

      // 清理文本内容
      content: this.cleanTextContent(order.content),
      title: this.cleanTextContent(order.title),

      // 游戏信息
      gameInfo: order.gameInfo || {
        gameName: this.cleanTextContent(order.title) || '高阶技术指导',
        gameMode: order.gameMode || null
      },

      // 价格信息
      pricing: order.pricing || {
        totalAmount: order.reward || order.totalAmount || 0
      },

      // 需求信息
      requirements: order.requirements || {
        description: order.content || '',
        duration: order.duration || 1,
        rounds: order.rounds || 5
      },

      // 抢单大厅特有字段
      canGrab: true,
      isGrabOrder: true,

      // 确保用户信息存在，保持 openid 字段
      customerInfo: order.customerInfo || {
        _id: order.customerId,
        nickName: '用户',
        avatarUrl: '',
        openid: order.customerOpenid || order._openid || '' // 尝试从不同字段获取 openid
      }
    };

    // 判断是否为订单发布者（实时格式化也需要这个字段）
    result.isOwner = this.isOrderOwner(result);

    console.log('🔍 [订单列表实时格式化] 最终platformType:', result.platformType);
    console.log('🔍 [订单列表实时格式化] 局部变量platformType:', platformType);
    console.log('🔍 [订单列表实时格式化] order.platformType:', order.platformType);
    console.log('🔍 [订单列表实时格式化] isOwner判断结果:', result.isOwner);
    return result;
  },

  // 兼容旧版本的处理方法
  handleOrderAddLegacy(snapshot) {
    const newOrders = snapshot.docs;
    if (newOrders.length > 0) {
      console.log('📡 [实时监听] 检测到新订单（兼容模式）:', newOrders.length, '个');
      newOrders.forEach(order => {
        // 只处理pending状态的订单
        if (order.status === 'pending') {
          this.handleOrderAdd(order);
        }
      });
    }
  },

  handleOrderUpdateLegacy(snapshot) {
    const updatedOrders = snapshot.docs;
    console.log('📡 [实时监听] 处理订单更新（兼容模式）:', updatedOrders.length, '个');
    updatedOrders.forEach(order => this.handleOrderUpdate(order));
  },

  handleOrderRemoveLegacy(snapshot) {
    const removedOrders = snapshot.docs;
    console.log('📡 [实时监听] 处理订单删除（兼容模式）:', removedOrders.length, '个');
    removedOrders.forEach(order => this.handleOrderRemove(order));
  },





  // 防抖更新机制
  debounceUpdate(updateFn, delay = 1000) {
    const now = Date.now();

    // 如果距离上次更新时间太短，使用防抖
    if (now - this.data.lastUpdateTime < delay) {
      if (this.data.updateTimer) {
        clearTimeout(this.data.updateTimer);
      }

      this.setData({
        updateTimer: setTimeout(() => {
          updateFn();
          this.setData({
            lastUpdateTime: Date.now(),
            updateTimer: null
          });
        }, delay)
      });
    } else {
      // 立即执行更新
      updateFn();
      this.setData({ lastUpdateTime: now });
    }
  },

  // 立即添加新订单到列表（无延迟）- 已废弃，使用新的单订单处理方式
  addNewOrdersToListInstantly(newOrders) {
    console.log('⚠️ [实时监听] 使用了废弃的批量添加方法，建议使用单订单处理');
    newOrders.forEach(order => this.handleOrderAdd(order));
  },

  // 直接添加新订单到列表（避免完整重新加载）- 保留原有方法作为备用
  async addNewOrdersToList(newOrders) {
    try {
      console.log('📡 [实时监听] 开始处理新订单，避免完整重新加载');

      // 格式化新订单数据
      const formattedNewOrders = await this.formatNewOrdersWithUserInfo(newOrders);

      // 获取当前订单列表
      const currentList = this.data.orderList || [];

      // 将新订单添加到列表开头（按时间倒序）
      const updatedList = [...formattedNewOrders, ...currentList];

      // 去重处理：根据订单ID去重
      const seen = new Set();
      const uniqueOrders = updatedList.filter(order => {
        if (seen.has(order._id)) {
          return false;
        }
        seen.add(order._id);
        return true;
      });

      // 更新列表
      this.setData({
        orderList: uniqueOrders
      });

      console.log('✅ [实时监听] 新订单已添加到列表，当前订单数:', uniqueOrders.length);

    } catch (error) {
      console.error('❌ [实时监听] 处理新订单失败，回退到完整重新加载:', error);
      // 如果直接添加失败，使用防抖机制回退到完整重新加载
      this.debounceUpdate(() => {
        this.loadOrderList(true);
      });
    }
  },

  // 格式化新订单并获取用户信息
  async formatNewOrdersWithUserInfo(newOrders) {
    try {
      // 获取所有需要的用户ID
      const customerIds = [...new Set(newOrders.map(order => order.customerId))];

      if (customerIds.length === 0) {
        return newOrders.map(order => this.formatOrderData(order));
      }

      // 查询用户信息
      const db = wx.cloud.database();
      const _ = db.command;

      const customerResult = await db.collection('users')
        .where({
          _id: _.in(customerIds)
        })
        .field({
          _id: true,
          nickName: true,
          avatarUrl: true,
          openid: true
        })
        .get();

      // 创建用户信息映射
      const customerMap = {};
      customerResult.data.forEach(customer => {
        customerMap[customer._id] = customer;
      });

      // 格式化订单数据
      const formattedOrders = newOrders.map(order => {
        const customer = customerMap[order.customerId] || {};

        // 添加客户信息
        const orderWithCustomer = {
          ...order,
          customerInfo: {
            _id: customer._id,
            nickName: customer.nickName || '未知用户',
            avatarUrl: customer.avatarUrl || '',
            openid: customer.openid || ''
          },
          // 抢单大厅特有字段
          canGrab: true,
          isGrabOrder: true
        };

        return this.formatOrderData(orderWithCustomer);
      });

      return formattedOrders;

    } catch (error) {
      console.error('❌ [实时监听] 格式化新订单失败:', error);
      // 如果格式化失败，返回基本格式化的订单
      return newOrders.map(order => this.formatOrderData(order));
    }
  },



  // 设置为个人订单模式（由tabBar调用）
  setPersonalMode() {
    console.log('🔄 切换到个人订单模式');

    // 停止实时监听
    this.stopOrderWatcher();

    this.setData({
      isGrabMode: false,
      fromTabBar: true,
      currentStatus: 'pending',
      currentStatusText: '待接单',
      statusTabs: [
        { label: '待接单', value: 'pending', count: 0 },
        { label: '已接单', value: 'accepted', count: 0 },
        { label: '进行中', value: 'in_progress', count: 0 }
      ]
    });

    // 更新页面标题
    wx.setNavigationBarTitle({
      title: '我的订单'
    });

    // 重新加载数据
    this.loadOrderList(true);
    // 加载订单统计
    this.loadOrderCounts();
  }
});
