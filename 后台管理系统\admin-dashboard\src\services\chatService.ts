// 聊天服务 - 直接调用微信云开发
import { callCloudFunction } from './wxCloudService';

// 获取聊天消息（使用现有的 chatMessage 云函数）
export const getChatMessages = async (chatRoomId: string, page = 1, pageSize = 50) => {
  console.log('🔍 [聊天服务] 开始获取聊天消息:', { chatRoomId, page, pageSize });
  
  try {
    // 调用现有的 chatMessage 云函数
    const response = await callCloudFunction('chatMessage', {
      action: 'get',
      chatRoomId: chatRoomId,
      page: page,
      pageSize: pageSize
    });
    
    console.log('📥 [聊天服务] 云函数响应:', response);
    
    if (!response.success) {
      throw new Error(response.error || '获取聊天消息失败');
    }
    
    // 转换数据格式以匹配前端期望
    const messages = response.data.list.map((msg: any) => ({
      id: msg._id,
      roomId: msg.chatRoomId,
      senderId: msg.senderId,
      content: msg.content,
      type: msg.type || 'text',
      timestamp: msg.createTime,
      isRead: true,
      senderInfo: msg.senderInfo || {
        nickName: '未知用户',
        avatarUrl: '/placeholder.svg?height=32&width=32'
      }
    }));
    
    console.log('✅ [聊天服务] 成功获取消息:', messages.length, '条');
    
    return {
      messages: messages,
      hasMore: response.data.hasMore || false,
      page: response.data.page || page
    };
    
  } catch (error) {
    console.error('❌ [聊天服务] 获取消息失败:', error);
    throw error;
  }
};

// 发送聊天消息（如果需要的话）
export const sendChatMessage = async (chatRoomId: string, content: string, type = 'text') => {
  console.log('📤 [聊天服务] 发送消息:', { chatRoomId, content, type });
  
  try {
    const response = await callCloudFunction('chatMessage', {
      action: 'send',
      chatRoomId: chatRoomId,
      content: content,
      type: type
    });
    
    if (!response.success) {
      throw new Error(response.error || '发送消息失败');
    }
    
    return response.data;
    
  } catch (error) {
    console.error('❌ [聊天服务] 发送消息失败:', error);
    throw error;
  }
};