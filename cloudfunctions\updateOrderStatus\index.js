// 更新订单状态云函数
const cloud = require('wx-server-sdk');

cloud.init({
  env: cloud.DYNAMIC_CURRENT_ENV
});

const db = cloud.database();

// 获取用户通知偏好设置
async function getUserNotificationPreferences(userId) {
  const defaultPrefs = {
    orderStatus: true,
    newOrder: true,
    chatMessage: true,
    evaluation: true,
    system: true,
    sound: true,
    vibrate: true
  };

  try {
    const userResult = await db.collection('users').doc(userId).get();
    if (userResult.data && userResult.data.notificationPreferences) {
      return { ...defaultPrefs, ...userResult.data.notificationPreferences };
    }
    return defaultPrefs;
  } catch (error) {
    console.error('获取用户通知偏好失败:', error);
    return defaultPrefs;
  }
}

exports.main = async (event, context) => {
  const wxContext = cloud.getWXContext();
  const { orderId, status, reason, updateData } = event;

  try {
    // 验证必填字段
    if (!orderId || !status) {
      return {
        success: false,
        error: '订单ID和状态不能为空'
      };
    }

    // 查找当前用户
    const userResult = await db.collection('users').where({
      openid: wxContext.OPENID
    }).get();

    if (userResult.data.length === 0) {
      return {
        success: false,
        error: '用户不存在'
      };
    }

    const user = userResult.data[0];

    // 查找订单
    const orderResult = await db.collection('orders').doc(orderId).get();

    if (!orderResult.data) {
      return {
        success: false,
        error: '订单不存在'
      };
    }

    const order = orderResult.data;

    // 检查用户权限 - 使用字符串比较以防ID类型不匹配
    const isCustomer = String(order.customerId) === String(user._id);
    const isAccepter = order.accepterId && String(order.accepterId) === String(user._id);

    // 调试信息
    console.log('=== 权限检查调试信息 ===');
    console.log('当前用户ID:', user._id, '(类型:', typeof user._id, ')');
    console.log('当前用户OpenID:', user.openid);
    console.log('订单发布者ID:', order.customerId, '(类型:', typeof order.customerId, ')');
    console.log('订单接单者ID:', order.accepterId, '(类型:', typeof order.accepterId, ')');
    console.log('订单状态:', order.status);
    console.log('目标状态:', status);
    console.log('字符串比较结果:');
    console.log('  String(order.customerId) === String(user._id):', String(order.customerId) === String(user._id));
    console.log('  String(order.accepterId) === String(user._id):', String(order.accepterId) === String(user._id));
    console.log('是否为发布者:', isCustomer);
    console.log('是否为接单者:', isAccepter);
    console.log('=== 权限检查调试信息结束 ===');

    if (!isCustomer && !isAccepter) {
      return {
        success: false,
        error: '您没有权限操作此订单'
      };
    }

    // 验证状态转换的合法性
    const validTransitions = {
      'pending': ['accepted', 'cancelled'],
      'waiting_match': ['accepted', 'cancelled'],
      'accepted': ['in_progress', 'cancelled'],
      'in_progress': ['completed', 'cancelled'],
      'completed': [],
      'cancelled': []
    };

    if (!validTransitions[order.status] || !validTransitions[order.status].includes(status)) {
      return {
        success: false,
        error: '无效的状态转换'
      };
    }

    // 根据状态执行不同的逻辑
    const finalUpdateData = {
      status: status,
      updateTime: new Date(),
      ...(updateData || {}) // 合并传入的额外更新数据
    };

    switch (status) {
      case 'in_progress':
        // 只有接单者可以开始服务
        if (!isAccepter) {
          return {
            success: false,
            error: '只有接单者可以开始服务'
          };
        }
        finalUpdateData.startTime = new Date();
        break;

      case 'completed':
        // 只有接单者可以完成订单
        if (!isAccepter) {
          return {
            success: false,
            error: '只有接单者可以完成订单'
          };
        }
        finalUpdateData.endTime = new Date();

        // 计算实际服务时长
        const startTime = new Date(order.startTime);
        const endTime = new Date();
        const actualDuration = Math.ceil((endTime - startTime) / (1000 * 60 * 60)); // 小时
        finalUpdateData.actualDuration = actualDuration;

        // 处理资金结算
        await handleOrderSettlement(order);

        // 发送评价提醒通知
        await sendEvaluationReminder(order);
        break;

      case 'cancelled':
        finalUpdateData.cancelTime = new Date();
        finalUpdateData.cancelReason = reason || '';
        finalUpdateData.cancelledBy = user._id;
        
        // 处理退款
        await handleOrderRefund(order);
        break;
    }

    // 更新订单
    await db.collection('orders').doc(orderId).update({
      data: finalUpdateData
    });

    // 更新相关统计信息
    if (status === 'completed') {
      // 更新接单者统计（可选，如果需要统计功能）
      // 这里可以添加用户完成订单数量的统计
    }

    // 发送状态变更通知
    try {
      const targetUserId = status === 'completed' ? order.customerId :
                          (isCustomer ? order.accepterId : order.customerId);

      if (targetUserId) {
        // 检查用户通知偏好
        const userPrefs = await getUserNotificationPreferences(targetUserId);
        if (!userPrefs.orderStatus) {
          console.log('用户已关闭订单状态通知，跳过发送');
        } else {
          const statusMessages = {
            'in_progress': '服务已开始',
            'completed': '订单已完成',
            'cancelled': '订单已取消'
          };

          await cloud.callFunction({
            name: 'sendNotification',
            data: {
              type: 'orderStatus',
              targetUserId: targetUserId,
              templateId: 'your_order_status_template_id',
              data: {
                title: '订单状态更新',
                content: statusMessages[status] || '订单状态已更新',
                orderNo: order.orderNo || orderId,
                status: status,
                statusText: statusMessages[status] || '已更新',
                orderTitle: order.title || '订单'
              },
              page: `order-package/pages/detail/detail?id=${orderId}`
            }
          });
        }
      }
    } catch (notificationError) {
      console.log('发送状态变更通知失败:', notificationError);
      // 通知失败不影响状态更新流程
    }

    return {
      success: true,
      message: '订单状态更新成功',
      data: {
        orderId: orderId,
        status: status
      }
    };
  } catch (error) {
    console.error('更新订单状态失败:', error);
    return {
      success: false,
      error: error.message
    };
  }
};

// 处理订单结算
async function handleOrderSettlement(order) {
  try {
    // 解冻客户资金
    await db.collection('transactions').where({
      orderId: order._id,
      type: 'payment',
      status: 'frozen'
    }).update({
      data: {
        status: 'completed',
        updateTime: new Date()
      }
    });

    // 给接单者增加收入
    await db.collection('users').doc(order.accepterId).update({
      data: {
        balance: db.command.inc(order.pricing.accepterAmount || order.pricing.companionAmount),
        updateTime: new Date()
      }
    });

    // 创建接单者收入记录
    await db.collection('transactions').add({
      data: {
        userId: order.accepterId,
        orderId: order._id,
        type: 'income',
        amount: order.pricing.accepterAmount || order.pricing.companionAmount,
        status: 'completed',
        description: `订单收入 - ${order.orderNo}`,
        createTime: new Date()
      }
    });

    console.log(`订单 ${order.orderNo} 结算完成`);
  } catch (error) {
    console.error('订单结算失败:', error);
    throw error;
  }
}

// 处理订单退款
async function handleOrderRefund(order) {
  try {
    // 解冻并退还客户资金
    await db.collection('transactions').where({
      orderId: order._id,
      type: 'payment',
      status: 'frozen'
    }).update({
      data: {
        status: 'refunded',
        updateTime: new Date()
      }
    });

    // 退还客户余额
    await db.collection('users').doc(order.customerId).update({
      data: {
        balance: db.command.inc(order.pricing.totalAmount),
        updateTime: new Date()
      }
    });

    // 创建退款记录
    await db.collection('transactions').add({
      data: {
        userId: order.customerId,
        orderId: order._id,
        type: 'refund',
        amount: order.pricing.totalAmount,
        status: 'completed',
        description: `订单退款 - ${order.orderNo}`,
        createTime: new Date()
      }
    });

    console.log(`订单 ${order.orderNo} 退款完成`);
  } catch (error) {
    console.error('订单退款失败:', error);
    throw error;
  }
}

// 发送评价提醒通知
async function sendEvaluationReminder(order) {
  try {
    console.log('发送评价提醒通知...');

    // 给客户发送评价提醒
    if (order.customerId) {
      await cloud.callFunction({
        name: 'sendNotification',
        data: {
          type: 'evaluationReminder',
          targetUserId: order.customerId,
          templateId: 'evaluation_reminder_template',
          data: {
            title: '订单已完成，请评价',
            content: '您的订单已完成，快来评价接单者的服务吧！',
            orderNo: order.orderNo || order._id,
            orderTitle: order.title || '订单',
            role: 'customer'
          },
          page: `order-package/pages/evaluation/evaluation?orderId=${order._id}`
        }
      });
    }

    // 给接单者发送评价提醒
    if (order.accepterId) {
      await cloud.callFunction({
        name: 'sendNotification',
        data: {
          type: 'evaluationReminder',
          targetUserId: order.accepterId,
          templateId: 'evaluation_reminder_template',
          data: {
            title: '订单已完成，请评价',
            content: '您完成的订单可以评价客户了！',
            orderNo: order.orderNo || order._id,
            orderTitle: order.title || '订单',
            role: 'accepter'
          },
          page: `order-package/pages/evaluation/evaluation?orderId=${order._id}`
        }
      });
    }

    console.log('评价提醒通知发送完成');
  } catch (error) {
    console.error('发送评价提醒通知失败:', error);
    // 通知失败不影响主流程
  }
}
